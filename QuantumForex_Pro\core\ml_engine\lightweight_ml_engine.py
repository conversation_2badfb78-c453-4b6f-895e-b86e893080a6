"""
QuantumForex Pro - 轻量级机器学习引擎
专为Windows Server 2012优化的高效ML模型
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import pickle
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 轻量级ML库导入
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.svm import SVC, SVR
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, mean_squared_error, classification_report
import joblib

from config.config import config

class ModelType(Enum):
    """模型类型枚举"""
    PRICE_PREDICTION = "price_prediction"
    TREND_CLASSIFICATION = "trend_classification"
    VOLATILITY_PREDICTION = "volatility_prediction"
    RISK_ASSESSMENT = "risk_assessment"

class PredictionHorizon(Enum):
    """预测时间范围枚举"""
    SHORT_TERM = "5min"      # 5分钟
    MEDIUM_TERM = "15min"    # 15分钟
    LONG_TERM = "1hour"      # 1小时

@dataclass
class MLPrediction:
    """ML预测结果数据结构"""
    model_type: ModelType
    prediction: float
    confidence: float
    probability_distribution: Dict[str, float]
    feature_importance: Dict[str, float]
    model_accuracy: float
    timestamp: str

class LightweightMLEngine:
    """
    轻量级机器学习引擎
    专为老服务器优化的高效ML系统
    """

    def __init__(self):
        self.config = config.ML_CONFIG
        self.models = {}
        self.scalers = {}
        self.feature_columns = {}
        self.model_performance = {}
        self.last_training_time = {}

        # 创建模型存储目录
        self.model_dir = os.path.join("QuantumForex_Pro", "data", "models")  # 修正路径
        os.makedirs(self.model_dir, exist_ok=True)

        # 优先尝试加载Trainer训练的高级模型
        trainer_models_loaded = self._load_trainer_models()

        if trainer_models_loaded:
            print("✅ 已加载Trainer训练的高级模型")
            # 为未加载的模型类型初始化标准模型
            self._initialize_missing_models()
        else:
            print("⚠️ 未找到Trainer模型，使用标准轻量级模型")
            # 初始化标准模型
            self._initialize_models()

    def _initialize_models(self):
        """初始化所有ML模型"""
        try:
            # 价格预测模型 (回归)
            self.models[ModelType.PRICE_PREDICTION] = RandomForestRegressor(
                **self.config['models']['random_forest']
            )

            # 趋势分类模型 (分类)
            self.models[ModelType.TREND_CLASSIFICATION] = RandomForestClassifier(
                **self.config['models']['random_forest']
            )

            # 波动率预测模型 (回归)
            self.models[ModelType.VOLATILITY_PREDICTION] = SVR(
                **self.config['models']['svm']
            )

            # 风险评估模型 (分类)
            self.models[ModelType.RISK_ASSESSMENT] = RandomForestClassifier(
                n_estimators=30,
                max_depth=6,
                random_state=42,
                n_jobs=2
            )

            # 初始化标准化器
            for model_type in ModelType:
                self.scalers[model_type] = StandardScaler()

            print("✅ ML模型初始化完成")

        except Exception as e:
            print(f"❌ ML模型初始化失败: {e}")

    def _load_trainer_models(self) -> bool:
        """加载Trainer训练的高级模型"""
        try:
            print("🔍 搜索Trainer训练的高级模型...")

            # 搜索Trainer模型文件（基于实际文件名格式）
            trainer_model_patterns = {
                ModelType.PRICE_PREDICTION: ['price_prediction_direction_5min_lightgbm', 'price_prediction_direction_5min_xgboost', 'price_prediction_direction_5min_random_forest'],
                ModelType.TREND_CLASSIFICATION: ['trend_classification_5min_lightgbm', 'trend_classification_5min_xgboost', 'trend_classification_5min_random_forest'],
                ModelType.VOLATILITY_PREDICTION: ['volatility_prediction_5min_lightgbm', 'volatility_prediction_5min_xgboost', 'volatility_prediction_5min_random_forest'],
                ModelType.RISK_ASSESSMENT: ['risk_assessment_5min_lightgbm', 'risk_assessment_5min_xgboost', 'risk_assessment_5min_random_forest']
            }

            loaded_models = 0

            for model_type, patterns in trainer_model_patterns.items():
                model_loaded = False

                for pattern in patterns:
                    # 搜索匹配模式的文件（因为文件名包含时间戳）
                    import glob
                    matching_files = glob.glob(os.path.join(self.model_dir, f"{pattern}_*.pkl"))

                    if matching_files:
                        # 选择最新的文件
                        model_file = max(matching_files, key=os.path.getmtime)
                        scaler_file = model_file.replace('_model.pkl', '_scaler.pkl')

                        # 如果没有对应的scaler文件，继续使用模型文件
                        if not os.path.exists(scaler_file):
                            scaler_file = None
                        try:
                            # 加载Trainer模型
                            trainer_model = joblib.load(model_file)

                            # 加载对应的缩放器（如果存在）
                            if scaler_file and os.path.exists(scaler_file):
                                trainer_scaler = joblib.load(scaler_file)
                            else:
                                # 如果没有缩放器，创建一个默认的
                                trainer_scaler = StandardScaler()

                            # 存储模型
                            self.models[model_type] = trainer_model
                            self.scalers[model_type] = trainer_scaler

                            # 设置高性能指标（Trainer模型通常更准确）
                            self.model_performance[model_type] = 0.85

                            # 获取模型信息
                            model_size = os.path.getsize(model_file)
                            model_mtime = datetime.fromtimestamp(os.path.getmtime(model_file))
                            model_filename = os.path.basename(model_file)

                            print(f"✅ 加载{model_type.value}: {model_filename}")
                            print(f"   大小: {model_size:,}字节, 时间: {model_mtime.strftime('%m-%d %H:%M')}")

                            loaded_models += 1
                            model_loaded = True
                            break  # 找到一个就停止搜索

                        except Exception as e:
                            print(f"⚠️ 加载{os.path.basename(model_file)}失败: {e}")
                            continue

                if not model_loaded:
                    print(f"❌ 未找到{model_type.value}的Trainer模型")

            if loaded_models > 0:
                print(f"🎉 成功加载{loaded_models}/{len(ModelType)}个Trainer高级模型")

                # 初始化未加载模型的缩放器
                for model_type in ModelType:
                    if model_type not in self.scalers:
                        self.scalers[model_type] = StandardScaler()

                return True
            else:
                print("❌ 未找到任何Trainer模型")
                return False

        except Exception as e:
            print(f"❌ 加载Trainer模型失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _initialize_missing_models(self):
        """为未加载Trainer模型的类型初始化标准模型"""
        try:
            print("🔧 为缺失的模型类型初始化标准模型...")

            missing_models = []

            for model_type in ModelType:
                if model_type not in self.models or self.models[model_type] is None:
                    missing_models.append(model_type)

            if not missing_models:
                print("✅ 所有模型类型都已加载")
                return

            print(f"📊 需要初始化{len(missing_models)}个标准模型: {[m.value for m in missing_models]}")

            for model_type in missing_models:
                if model_type == ModelType.PRICE_PREDICTION:
                    self.models[model_type] = RandomForestRegressor(
                        **self.config['models']['random_forest']
                    )
                elif model_type == ModelType.TREND_CLASSIFICATION:
                    self.models[model_type] = RandomForestClassifier(
                        **self.config['models']['random_forest']
                    )
                elif model_type == ModelType.VOLATILITY_PREDICTION:
                    self.models[model_type] = SVR(
                        **self.config['models']['svm']
                    )
                elif model_type == ModelType.RISK_ASSESSMENT:
                    self.models[model_type] = RandomForestClassifier(
                        n_estimators=30,
                        max_depth=6,
                        random_state=42,
                        n_jobs=2
                    )

                # 初始化对应的标准化器
                if model_type not in self.scalers:
                    self.scalers[model_type] = StandardScaler()

                # 设置标准性能指标
                self.model_performance[model_type] = 0.5

                print(f"✅ 初始化{model_type.value}标准模型: {type(self.models[model_type]).__name__}")

            print(f"✅ 标准模型初始化完成: {len(missing_models)}个")

        except Exception as e:
            print(f"❌ 标准模型初始化失败: {e}")

    def generate_predictions(self, market_data: pd.DataFrame, technical_indicators: Dict) -> Dict[ModelType, MLPrediction]:
        """
        生成ML预测

        Args:
            market_data: 市场数据
            technical_indicators: 技术指标

        Returns:
            Dict: 各模型的预测结果
        """
        predictions = {}

        try:
            # 特征工程
            features = self._engineer_features(market_data, technical_indicators)

            if features is None or len(features) == 0:
                return self._create_default_predictions()

            # 生成各类预测
            for model_type in ModelType:
                try:
                    prediction = self._generate_single_prediction(model_type, features)
                    predictions[model_type] = prediction
                except Exception as e:
                    print(f"⚠️ {model_type.value}预测失败: {e}")
                    predictions[model_type] = self._create_default_prediction(model_type)

            return predictions

        except Exception as e:
            print(f"❌ ML预测生成失败: {e}")
            return self._create_default_predictions()

    def _engineer_features(self, market_data: pd.DataFrame, technical_indicators: Dict) -> Optional[pd.DataFrame]:
        """增强特征工程 - 充分利用历史数据"""
        try:
            min_data_points = 30  # 降低最小数据点要求
            if len(market_data) < min_data_points:
                print(f"⚠️ 数据点不足: {len(market_data)} < {min_data_points}")
                return None

            features = pd.DataFrame(index=market_data.index)

            # 1. 基础价格特征
            features['price_change'] = market_data['close'].pct_change()
            features['price_volatility'] = market_data['close'].rolling(10, min_periods=5).std()
            features['price_momentum'] = market_data['close'] / market_data['close'].shift(5) - 1

            # 2. 增强价格特征
            features['price_range'] = (market_data['high'] - market_data['low']) / market_data['close']
            features['price_position'] = (market_data['close'] - market_data['low']) / (market_data['high'] - market_data['low'])
            features['gap'] = (market_data['open'] - market_data['close'].shift(1)) / market_data['close'].shift(1)

            # 3. 多时间框架移动平均
            for period in [5, 10, 20]:
                ma = market_data['close'].rolling(period, min_periods=max(1, period//2)).mean()
                features[f'ma_{period}_ratio'] = market_data['close'] / ma - 1
                features[f'ma_{period}_slope'] = ma.diff(3) / ma.shift(3)

            # 2. 成交量特征
            if 'volume' in market_data.columns:
                features['volume_change'] = market_data['volume'].pct_change()
                features['volume_ma_ratio'] = market_data['volume'] / market_data['volume'].rolling(10).mean()

            # 3. 技术指标特征
            if 'trend_analysis' in technical_indicators:
                trend_data = technical_indicators['trend_analysis']
                if 'trend_score' in trend_data:
                    features['trend_score'] = trend_data['trend_score']

                # ADX特征
                adx_data = trend_data.get('adx_analysis', {})
                if 'adx' in adx_data:
                    features['adx'] = adx_data['adx']
                    features['plus_di'] = adx_data.get('plus_di', 0)
                    features['minus_di'] = adx_data.get('minus_di', 0)

            if 'momentum_analysis' in technical_indicators:
                momentum_data = technical_indicators['momentum_analysis']
                if 'momentum_score' in momentum_data:
                    features['momentum_score'] = momentum_data['momentum_score']

                # RSI特征
                rsi_data = momentum_data.get('rsi_analysis', {})
                if 'rsi_data' in rsi_data:
                    rsi_values = rsi_data['rsi_data']
                    if 'rsi_14' in rsi_values:
                        features['rsi_14'] = rsi_values['rsi_14']

            # 4. 波动率特征
            if 'volatility_analysis' in technical_indicators:
                vol_data = technical_indicators['volatility_analysis']
                if 'volatility_score' in vol_data:
                    features['volatility_score'] = vol_data['volatility_score']

            # 5. 时间特征
            if hasattr(market_data.index, 'hour'):
                features['hour'] = market_data.index.hour
                features['day_of_week'] = market_data.index.dayofweek

            # 6. 滞后特征
            for lag in [1, 2, 3, 5]:
                features[f'price_change_lag_{lag}'] = features['price_change'].shift(lag)
                features[f'volume_change_lag_{lag}'] = features.get('volume_change', 0).shift(lag)

            # 7. 移动平均特征
            for window in [5, 10, 20]:
                features[f'price_ma_{window}'] = market_data['close'].rolling(window).mean()
                features[f'price_std_{window}'] = market_data['close'].rolling(window).std()

            # 删除NaN值
            features = features.dropna()

            # 限制特征数量 (适配老服务器)
            max_features = self.config['feature_engineering']['max_features']
            if len(features.columns) > max_features:
                # 选择最重要的特征
                features = features.iloc[:, :max_features]

            return features

        except Exception as e:
            print(f"❌ 特征工程失败: {e}")
            return None

    def _generate_single_prediction(self, model_type: ModelType, features: pd.DataFrame) -> MLPrediction:
        """生成单个模型的预测"""
        try:
            model = self.models[model_type]
            scaler = self.scalers[model_type]

            # 检查模型是否已训练
            # 🚫 禁用Pro自带训练功能，避免与Trainer模型冲突
            # 如果使用Trainer模型，不再进行在线训练
            trainer_model_performance = self.model_performance.get(model_type, 0.5)

            if trainer_model_performance >= 0.8:
                # 这是Trainer训练的高级模型，不进行在线训练
                pass
            elif not hasattr(model, 'feature_importances_') and not hasattr(model, 'coef_'):
                # 只有标准模型且未训练时才进行在线训练
                print(f"⚠️ {model_type.value}使用标准模型，考虑使用Trainer训练的高级模型")
                # 暂时禁用在线训练，避免与Trainer模型冲突
                # self._train_model_if_needed(model_type, features)

            # 准备预测数据
            latest_features = features.iloc[-1:].values

            # 标准化特征
            if hasattr(scaler, 'mean_'):
                latest_features_scaled = scaler.transform(latest_features)
            else:
                # 如果scaler未拟合，使用原始特征
                latest_features_scaled = latest_features

            # 生成预测
            if model_type in [ModelType.PRICE_PREDICTION, ModelType.VOLATILITY_PREDICTION]:
                # 回归预测
                prediction = model.predict(latest_features_scaled)[0]
                confidence = self._calculate_regression_confidence(model, latest_features_scaled)
                probability_distribution = {'prediction': float(prediction)}

            else:
                # 分类预测
                prediction_proba = model.predict_proba(latest_features_scaled)[0]
                prediction = model.predict(latest_features_scaled)[0]
                confidence = max(prediction_proba)

                # 构建概率分布
                classes = model.classes_
                probability_distribution = {
                    str(cls): float(prob) for cls, prob in zip(classes, prediction_proba)
                }

            # 特征重要性
            feature_importance = self._get_feature_importance(model, features.columns)

            # 模型准确性
            model_accuracy = self.model_performance.get(model_type, 0.5)

            return MLPrediction(
                model_type=model_type,
                prediction=float(prediction),
                confidence=float(confidence),
                probability_distribution=probability_distribution,
                feature_importance=feature_importance,
                model_accuracy=float(model_accuracy),
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            print(f"❌ {model_type.value}预测失败: {e}")
            return self._create_default_prediction(model_type)

    def _train_model_if_needed(self, model_type: ModelType, features: pd.DataFrame):
        """智能按需训练模型"""
        try:
            # 检查模型是否已存在且已训练
            model = self.models[model_type]
            last_training = self.last_training_time.get(model_type)

            # 检查模型是否已经训练过
            is_model_trained = (hasattr(model, 'feature_importances_') or
                              hasattr(model, 'coef_') or
                              hasattr(model, 'support_vectors_'))

            # 训练策略：
            # 1. 如果模型从未训练过 -> 立即训练
            # 2. 如果已训练过 -> 根据时间间隔决定是否重训练
            should_train = False
            train_reason = ""

            if not is_model_trained:
                should_train = True
                train_reason = "首次训练"
            elif last_training is None:
                should_train = True
                train_reason = "无训练记录"
            else:
                # 检查是否需要重新训练（6小时间隔）
                hours_since_training = (datetime.now() - last_training).total_seconds() / 3600
                retrain_interval = self.config['training'].get('retrain_interval_hours', 6)

                if hours_since_training >= retrain_interval:
                    should_train = True
                    train_reason = f"定期重训练 ({hours_since_training:.1f}小时前)"
                else:
                    print(f"🔄 {model_type.value}模型使用已训练版本 (训练于{hours_since_training:.1f}小时前)")
                    return

            if should_train:
                print(f"🔄 开始训练{model_type.value}模型 ({train_reason})...")

                # 生成训练标签
                X, y = self._prepare_training_data(model_type, features)

                # 使用优化后的训练数据要求
                min_samples = self.config['training']['min_training_samples']
                optimal_samples = self.config['training'].get('optimal_training_samples', 200)

                if len(X) >= min_samples:
                    # 训练模型
                    self._train_model(model_type, X, y)
                    self.last_training_time[model_type] = datetime.now()

                    # 根据数据量给出不同的反馈
                    if len(X) >= optimal_samples:
                        print(f"✅ {model_type.value}模型训练完成 (样本数: {len(X)}) - 数据充足")
                    elif len(X) >= min_samples * 2:
                        print(f"✅ {model_type.value}模型训练完成 (样本数: {len(X)}) - 数据良好")
                    else:
                        print(f"✅ {model_type.value}模型训练完成 (样本数: {len(X)}) - 数据基础")
                else:
                    print(f"⚠️ {model_type.value}训练数据不足 (需要{min_samples}个，当前{len(X)}个)")

        except Exception as e:
            print(f"❌ {model_type.value}模型训练失败: {e}")

    def _prepare_training_data(self, model_type: ModelType, features: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        try:
            X = features.values

            if model_type == ModelType.PRICE_PREDICTION:
                # 价格预测：预测下一期收益率
                y = features['price_change'].shift(-1).dropna().values
                X = X[:-1]  # 对应调整X的长度

            elif model_type == ModelType.TREND_CLASSIFICATION:
                # 趋势分类：预测趋势方向
                price_change = features['price_change'].shift(-1)
                y = np.where(price_change > 0.001, 1, np.where(price_change < -0.001, -1, 0))
                y = y[:-1]  # 去掉最后一个NaN
                X = X[:-1]

            elif model_type == ModelType.VOLATILITY_PREDICTION:
                # 波动率预测：预测下一期波动率
                y = features['price_volatility'].shift(-1).dropna().values
                X = X[:-1]

            elif model_type == ModelType.RISK_ASSESSMENT:
                # 风险评估：基于波动率分类风险等级
                volatility = features['price_volatility']
                y = np.where(volatility > volatility.quantile(0.8), 2,  # 高风险
                           np.where(volatility > volatility.quantile(0.6), 1, 0))  # 中风险, 低风险

            # 移除NaN值
            valid_indices = ~(np.isnan(X).any(axis=1) | np.isnan(y))
            X = X[valid_indices]
            y = y[valid_indices]

            return X, y

        except Exception as e:
            print(f"❌ 训练数据准备失败: {e}")
            return np.array([]), np.array([])

    def _train_model(self, model_type: ModelType, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        try:
            model = self.models[model_type]
            scaler = self.scalers[model_type]

            # 数据分割
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=self.config['training']['validation_split'], random_state=42
            )

            # 特征标准化
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # 训练模型
            model.fit(X_train_scaled, y_train)

            # 评估模型
            if model_type in [ModelType.PRICE_PREDICTION, ModelType.VOLATILITY_PREDICTION]:
                # 回归评估
                y_pred = model.predict(X_test_scaled)
                mse = mean_squared_error(y_test, y_pred)
                accuracy = max(0, 1 - mse)  # 简化的准确性度量
            else:
                # 分类评估
                y_pred = model.predict(X_test_scaled)
                accuracy = accuracy_score(y_test, y_pred)

            self.model_performance[model_type] = accuracy

            # 保存模型
            self._save_model(model_type, model, scaler)

        except Exception as e:
            print(f"❌ 模型训练失败: {e}")

    def _calculate_regression_confidence(self, model, features: np.ndarray) -> float:
        """计算回归模型的置信度"""
        try:
            # 基于特征重要性和模型性能计算置信度
            if hasattr(model, 'feature_importances_'):
                # 随机森林的置信度基于树的一致性
                predictions = []
                for estimator in model.estimators_[:10]:  # 只使用前10棵树以节省计算
                    pred = estimator.predict(features)[0]
                    predictions.append(pred)

                # 计算预测的标准差，标准差越小置信度越高
                std = np.std(predictions)
                confidence = max(0.1, 1.0 - min(std * 10, 0.9))
                return confidence
            else:
                return 0.5

        except Exception:
            return 0.5

    def _get_feature_importance(self, model, feature_names: List[str]) -> Dict[str, float]:
        """获取特征重要性"""
        try:
            if hasattr(model, 'feature_importances_'):
                importance_dict = {}
                for name, importance in zip(feature_names, model.feature_importances_):
                    importance_dict[name] = float(importance)
                return importance_dict
            else:
                return {}
        except Exception:
            return {}

    def _should_retrain(self, last_training: datetime, frequency: str) -> bool:
        """判断是否需要重新训练"""
        now = datetime.now()

        if frequency == 'daily':
            return (now - last_training).days >= 1
        elif frequency == 'weekly':
            return (now - last_training).days >= 7
        elif frequency == 'monthly':
            return (now - last_training).days >= 30
        else:
            return False

    def _save_model(self, model_type: ModelType, model, scaler):
        """保存模型"""
        try:
            # 🚫 禁用Pro自带模型保存，避免覆盖Trainer模型
            # 检查是否是Trainer模型
            trainer_model_performance = self.model_performance.get(model_type, 0.5)

            if trainer_model_performance >= 0.8:
                print(f"🚫 跳过保存{model_type.value}模型：使用Trainer高级模型，避免覆盖")
                return

            # 只保存标准模型（但目前已禁用训练，所以这里不会执行）
            model_path = os.path.join(self.model_dir, f"{model_type.value}_model.pkl")
            scaler_path = os.path.join(self.model_dir, f"{model_type.value}_scaler.pkl")

            joblib.dump(model, model_path)
            joblib.dump(scaler, scaler_path)
            print(f"💾 保存标准模型: {model_type.value}")

        except Exception as e:
            print(f"❌ 模型保存失败: {e}")

    def _load_model(self, model_type: ModelType) -> bool:
        """加载模型"""
        try:
            model_path = os.path.join(self.model_dir, f"{model_type.value}_model.pkl")
            scaler_path = os.path.join(self.model_dir, f"{model_type.value}_scaler.pkl")

            if os.path.exists(model_path) and os.path.exists(scaler_path):
                self.models[model_type] = joblib.load(model_path)
                self.scalers[model_type] = joblib.load(scaler_path)
                return True
            return False

        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False

    def _create_default_predictions(self) -> Dict[ModelType, MLPrediction]:
        """创建默认预测"""
        predictions = {}
        for model_type in ModelType:
            predictions[model_type] = self._create_default_prediction(model_type)
        return predictions

    def _create_default_prediction(self, model_type: ModelType) -> MLPrediction:
        """创建默认预测"""
        return MLPrediction(
            model_type=model_type,
            prediction=0.0,
            confidence=0.1,
            probability_distribution={'default': 1.0},
            feature_importance={},
            model_accuracy=0.5,
            timestamp=datetime.now().isoformat()
        )
