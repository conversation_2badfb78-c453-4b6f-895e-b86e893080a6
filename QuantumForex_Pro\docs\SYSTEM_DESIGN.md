# QuantumForex Pro 系统设计文档

## 🔧 重要修复记录

### 2025-05-30: 对冲逻辑修复 (关键修复)

**问题描述：**
- 原始对冲逻辑存在严重错误，导致风险加倍而非对冲
- GBP_USD_HEDGE组合：GBPUSD BUY + USDCHF SELL = 双重做空USD (-0.06 USD敞口)
- EUR_USD_HEDGE组合：EURUSD BUY + USDCHF SELL = 双重做空USD (-0.06 USD敞口)

**修复内容：**
1. **新增动态对冲方向计算**：`_calculate_hedge_directions()`
   - 分析每个货币对中USD的位置
   - 动态调整交易方向确保USD敞口对冲

2. **新增USD敞口计算**：`_calculate_usd_exposure()`
   - USD作为报价货币：BUY = 做空USD，SELL = 做多USD
   - USD作为基础货币：BUY = 做多USD，SELL = 做空USD

3. **修复后的对冲逻辑**：
   - GBP_USD_HEDGE：GBPUSD BUY (-0.03 USD) + USDCHF BUY (+0.03 USD) = 0 USD敞口 ✅
   - EUR_USD_HEDGE：EURUSD BUY (-0.03 USD) + USDCHF BUY (+0.03 USD) = 0 USD敞口 ✅

**验证结果：**
- ✅ 3/3 对冲方向计算测试通过
- ✅ 10/10 USD敞口计算测试通过
- ✅ 组合决策测试通过
- ✅ 真实场景模拟验证成功

**影响范围：**
- 文件：`core/portfolio_manager/combo_trading_manager.py`
- 功能：所有对冲交易组合
- 风险：修复前系统执行的是错误对冲，增加而非降低风险

## 🎯 设计目标

创建世界顶级的量化交易系统，实现：
- **稳定收益**：年化收益率25-40%，月收益率波动<5%
- **高胜率**：交易胜率>60%，盈亏比>1.8
- **低回撤**：最大回撤<12%，日最大亏损<3%

## 🏗️ 系统架构设计

### 四层智能架构

```
┌─────────────────────────────────────────────────────────────┐
│                    第四层：LLM策略大脑                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 宏观分析器   │ │ 参数优化器   │ │ 风险评估器   │ │ 决策协调器   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  第三层：多因子信号融合引擎                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 技术分析因子 │ │ 基本面因子   │ │ 微观结构因子 │ │ 情绪因子     │ │
│  │ 50+指标     │ │ 经济数据     │ │ 订单流分析   │ │ 资金流向     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  第二层：机器学习预测引擎                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 价格预测模型 │ │ 波动率模型   │ │ 趋势识别模型 │ │ 风险预警模型 │ │
│  │ RandomForest│ │ GARCH模型    │ │ LSTM轻量版  │ │ SVM分类器   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    第一层：高效执行引擎                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 订单路由器   │ │ 风险监控器   │ │ 仓位管理器   │ │ 性能统计器   │ │
│  │ 智能执行     │ │ 实时监控     │ │ 动态调整     │ │ 实时分析     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔬 技术分析体系

### A. 趋势分析 (40%权重)
```python
趋势指标集合 = {
    # 移动平均线系统
    'adaptive_ma': ['Kaufman_AMA', 'MESA_Adaptive_MA', 'Hull_MA'],
    'ma_crossover': ['EMA_8_21', 'SMA_20_50', 'EMA_12_26'],

    # 趋势强度指标
    'trend_strength': ['ADX', 'Aroon', 'Chande_Momentum'],
    'trend_quality': ['Linear_Regression_Slope', 'R_Squared'],

    # 多时间框架确认
    'mtf_trend': ['15min_trend', '1h_trend', '4h_trend', 'daily_trend']
}
```

### B. 动量分析 (30%权重)
```python
动量指标集合 = {
    # 高级RSI变种
    'rsi_variants': ['Connors_RSI', 'Stochastic_RSI', 'RSI_Divergence'],

    # 动量振荡器
    'momentum_osc': ['MACD_Histogram', 'ROC', 'Momentum_Oscillator'],

    # 相对强度分析
    'relative_strength': ['RS_vs_Market', 'Sector_Rotation'],

    # 速度指标
    'velocity': ['Price_Velocity', 'Acceleration_Bands']
}
```

### C. 波动率分析 (20%权重)
```python
波动率指标集合 = {
    # 动态布林带
    'bollinger_variants': ['Adaptive_BB', 'BB_Squeeze', 'BB_Width'],

    # 波动率估计器
    'volatility_est': ['Parkinson', 'Garman_Klass', 'Rogers_Satchell'],

    # 波动率突破
    'vol_breakout': ['Keltner_Channels', 'Donchian_Channels'],

    # 隐含波动率
    'implied_vol': ['VIX_Style_Index', 'GARCH_Forecast']
}
```

### D. 成交量分析 (10%权重)
```python
成交量指标集合 = {
    # 价量关系
    'price_volume': ['VPT', 'OBV', 'Accumulation_Distribution'],

    # 资金流指标
    'money_flow': ['Chaikin_MF', 'Money_Flow_Index', 'Elder_Force'],

    # 成交量分布
    'volume_profile': ['VWAP_Bands', 'Volume_Profile', 'POC'],

    # 机构行为
    'institutional': ['Large_Order_Detection', 'Block_Trade_Analysis']
}
```

## 🤖 机器学习模型设计

### 轻量级模型集合（适配Windows Server 2012）

```python
ML_Models = {
    # 价格预测模型
    'price_prediction': {
        'model': 'RandomForestRegressor',
        'params': {'n_estimators': 50, 'max_depth': 10, 'random_state': 42},
        'features': ['technical_indicators', 'price_patterns', 'volume_features'],
        'target': 'next_period_return',
        'memory_usage': 'Low'
    },

    # 波动率预测模型
    'volatility_prediction': {
        'model': 'GARCH(1,1)',
        'params': {'p': 1, 'q': 1},
        'features': ['historical_volatility', 'volume_volatility'],
        'target': 'next_period_volatility',
        'memory_usage': 'Very Low'
    },

    # 趋势识别模型
    'trend_classification': {
        'model': 'SVM_Classifier',
        'params': {'kernel': 'rbf', 'C': 1.0, 'gamma': 'scale'},
        'features': ['trend_indicators', 'momentum_indicators'],
        'target': 'trend_direction',
        'memory_usage': 'Low'
    },

    # 风险预警模型
    'risk_warning': {
        'model': 'GradientBoostingClassifier',
        'params': {'n_estimators': 30, 'max_depth': 6, 'learning_rate': 0.1},
        'features': ['volatility_features', 'correlation_features', 'drawdown_features'],
        'target': 'risk_level',
        'memory_usage': 'Medium'
    }
}
```

## 🛡️ 风险管理体系

### 多层次风险控制

```python
风险管理层级 = {
    # 账户级风险
    'account_level': {
        'max_drawdown': 0.12,        # 最大回撤12%
        'daily_loss_limit': 0.03,    # 日亏损限制3%
        'weekly_loss_limit': 0.08,   # 周亏损限制8%
        'monthly_loss_limit': 0.15   # 月亏损限制15%
    },

    # 策略级风险
    'strategy_level': {
        'max_single_position': 0.02,  # 单笔最大风险2%
        'max_strategy_exposure': 0.10, # 单策略最大暴露10%
        'correlation_limit': 0.7,      # 相关性限制0.7
        'max_positions': 8             # 最大持仓数量8个
    },

    # 品种级风险
    'instrument_level': {
        'max_currency_exposure': 0.15,  # 单货币最大暴露15%
        'max_pair_positions': 2,        # 单货币对最大持仓2个
        'liquidity_requirement': 'High', # 流动性要求
        'spread_limit': 3               # 点差限制3点
    },

    # 时间级风险
    'temporal_level': {
        'max_holding_period': 72,      # 最大持仓时间72小时
        'news_event_buffer': 30,       # 新闻事件缓冲30分钟
        'market_close_buffer': 60,     # 市场关闭缓冲60分钟
        'weekend_position_limit': 0.5  # 周末持仓限制50%
    }
}
```

## 🧠 LLM策略大脑设计

### 四大核心模块

```python
LLM_Brain_Modules = {
    # 宏观分析器
    'macro_analyzer': {
        'input': ['global_economic_data', 'central_bank_policies', 'geopolitical_events'],
        'output': ['market_regime', 'currency_ranking', 'risk_appetite'],
        'frequency': 'daily',
        'token_budget': 5000
    },

    # 参数优化器
    'parameter_optimizer': {
        'input': ['strategy_performance', 'market_conditions', 'risk_metrics'],
        'output': ['optimized_parameters', 'strategy_weights', 'risk_adjustments'],
        'frequency': 'weekly',
        'token_budget': 8000
    },

    # 风险评估器
    'risk_assessor': {
        'input': ['portfolio_status', 'market_volatility', 'correlation_matrix'],
        'output': ['risk_level', 'position_adjustments', 'hedge_recommendations'],
        'frequency': 'daily',
        'token_budget': 3000
    },

    # 决策协调器
    'decision_coordinator': {
        'input': ['all_signals', 'risk_constraints', 'market_context'],
        'output': ['final_decisions', 'execution_priority', 'monitoring_alerts'],
        'frequency': 'hourly',
        'token_budget': 4000
    }
}
```

## 📊 性能目标与监控

### 关键性能指标(KPI)

```python
Performance_Targets = {
    # 收益指标
    'return_metrics': {
        'annual_return': {'target': 0.30, 'min': 0.25, 'max': 0.40},
        'monthly_return': {'target': 0.025, 'volatility': 0.05},
        'daily_return': {'target': 0.001, 'volatility': 0.02}
    },

    # 风险指标
    'risk_metrics': {
        'max_drawdown': {'limit': 0.12, 'warning': 0.08},
        'sharpe_ratio': {'target': 2.0, 'min': 1.5},
        'calmar_ratio': {'target': 2.5, 'min': 2.0},
        'var_95': {'limit': 0.03}  # 95% VaR限制3%
    },

    # 交易指标
    'trading_metrics': {
        'win_rate': {'target': 0.65, 'min': 0.60},
        'profit_factor': {'target': 1.8, 'min': 1.5},
        'avg_trade_duration': {'target': 24, 'max': 72},  # 小时
        'max_consecutive_losses': {'limit': 4}
    },

    # 系统指标
    'system_metrics': {
        'signal_latency': {'max': 30},  # 秒
        'execution_slippage': {'max': 1},  # 点
        'system_uptime': {'min': 0.99},
        'memory_usage': {'max': 0.70}  # 70%
    }
}
```

## 🔄 系统工作流程

### 主要执行循环

```python
def main_execution_loop():
    """主执行循环 - 每分钟执行一次"""

    # 1. 数据收集 (5秒)
    market_data = collect_market_data()

    # 2. 信号生成 (10秒)
    technical_signals = generate_technical_signals(market_data)
    ml_predictions = generate_ml_predictions(market_data)

    # 3. 信号融合 (5秒)
    composite_signals = fuse_signals(technical_signals, ml_predictions)

    # 4. 风险检查 (5秒)
    risk_assessment = assess_risk(composite_signals)

    # 5. 决策制定 (5秒)
    trading_decisions = make_decisions(composite_signals, risk_assessment)

    # 6. 执行交易 (5秒)
    if trading_decisions:
        execute_trades(trading_decisions)

    # 7. 监控更新 (5秒)
    update_monitoring_dashboard()

    # 总执行时间: ~40秒，留20秒缓冲
```

### LLM优化循环

```python
def llm_optimization_loop():
    """LLM优化循环 - 每4小时执行一次"""

    # 1. 性能分析
    performance_data = analyze_recent_performance()

    # 2. 市场状态分析
    market_analysis = llm_brain.analyze_market_conditions()

    # 3. 参数优化
    optimized_params = llm_brain.optimize_parameters(performance_data)

    # 4. 策略调整
    strategy_adjustments = llm_brain.adjust_strategies(market_analysis)

    # 5. 风险重评估
    risk_adjustments = llm_brain.reassess_risk_parameters()

    # 6. 应用更新
    apply_system_updates(optimized_params, strategy_adjustments, risk_adjustments)
```

---

**下一步**：开始核心引擎开发，从信号生成引擎开始实现。
