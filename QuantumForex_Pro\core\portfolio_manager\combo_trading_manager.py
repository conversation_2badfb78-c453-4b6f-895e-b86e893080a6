#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
组合交易管理器
解决集中建仓问题，实现对冲、套利、分散等高级交易策略
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ComboType(Enum):
    """组合类型"""
    HEDGE_PAIR = "hedge_pair"           # 货币对冲
    DIRECTION_HEDGE = "direction_hedge" # 方向对冲
    CORRELATION_HEDGE = "correlation_hedge" # 相关性对冲
    ARBITRAGE = "arbitrage"             # 套利组合
    ROTATION = "rotation"               # 轮换交易
    STAGGERED = "staggered"            # 分批建仓

@dataclass
class ComboTrade:
    """组合交易"""
    combo_id: str
    combo_type: ComboType
    symbols: List[str]
    directions: List[str]  # ['long', 'short', ...]
    position_sizes: List[float]
    entry_times: List[datetime]
    target_correlation: float
    risk_level: str  # 'low', 'medium', 'high'
    description: str

@dataclass
class ComboDecision:
    """组合决策"""
    action: str  # 'create_combo', 'add_to_combo', 'close_combo', 'hold'
    combo_trades: List[ComboTrade]
    reason: str
    confidence: float
    expected_return: float
    max_risk: float

class ComboTradingManager:
    """组合交易管理器

    核心功能：
    1. 对冲策略 - 降低方向性风险
    2. 套利策略 - 利用价格差异
    3. 分散建仓 - 避免集中风险
    4. 轮换交易 - 时间分散
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 货币对相关性矩阵（简化版）
        self.correlation_matrix = {
            'EURUSD': {'GBPUSD': 0.7, 'AUDUSD': 0.6, 'NZDUSD': 0.5, 'USDCHF': -0.8, 'USDCAD': -0.3, 'USDJPY': -0.2},
            'GBPUSD': {'EURUSD': 0.7, 'AUDUSD': 0.5, 'NZDUSD': 0.4, 'USDCHF': -0.6, 'USDCAD': -0.2, 'USDJPY': -0.1},
            'AUDUSD': {'EURUSD': 0.6, 'GBPUSD': 0.5, 'NZDUSD': 0.8, 'USDCHF': -0.4, 'USDCAD': 0.3, 'USDJPY': 0.1},
            'NZDUSD': {'EURUSD': 0.5, 'GBPUSD': 0.4, 'AUDUSD': 0.8, 'USDCHF': -0.3, 'USDCAD': 0.2, 'USDJPY': 0.0},
            'USDCHF': {'EURUSD': -0.8, 'GBPUSD': -0.6, 'AUDUSD': -0.4, 'NZDUSD': -0.3, 'USDCAD': 0.2, 'USDJPY': 0.3},
            'USDCAD': {'EURUSD': -0.3, 'GBPUSD': -0.2, 'AUDUSD': 0.3, 'NZDUSD': 0.2, 'USDCHF': 0.2, 'USDJPY': 0.1},
            'USDJPY': {'EURUSD': -0.2, 'GBPUSD': -0.1, 'AUDUSD': 0.1, 'NZDUSD': 0.0, 'USDCHF': 0.3, 'USDCAD': 0.1}
        }

        # 对冲组合配置
        self.hedge_pairs = {
            'EUR_USD_HEDGE': ['EURUSD', 'USDCHF'],  # 欧元对冲
            'GBP_USD_HEDGE': ['GBPUSD', 'USDCHF'],  # 英镑对冲
            'AUD_NZD_HEDGE': ['AUDUSD', 'NZDUSD'],  # 澳纽对冲
            'COMMODITY_HEDGE': ['AUDUSD', 'USDCAD'] # 商品货币对冲
        }

        # 当前活跃组合
        self.active_combos = {}
        self.last_combo_time = {}

    def analyze_combo_opportunities(self, signals: List[Dict], current_positions: Dict) -> ComboDecision:
        """分析组合交易机会

        Args:
            signals: 单个货币对的交易信号列表
            current_positions: 当前持仓情况

        Returns:
            ComboDecision: 组合交易决策
        """
        try:
            if not signals:
                return ComboDecision(
                    action='hold',
                    combo_trades=[],
                    reason="无可用信号",
                    confidence=0.0,
                    expected_return=0.0,
                    max_risk=0.0
                )

            # 1. 检查是否需要对冲现有仓位（最高优先级）
            hedge_decision = self._analyze_hedge_opportunities(signals, current_positions)
            if hedge_decision.action == 'create_combo':
                self.logger.info(f"🛡️ 对冲组合优先执行: {hedge_decision.reason}")
                return hedge_decision

            # 2. 检查套利机会
            arbitrage_decision = self._analyze_arbitrage_opportunities(signals)
            if arbitrage_decision.action != 'hold':
                return arbitrage_decision

            # 3. 检查分散建仓机会
            stagger_decision = self._analyze_staggered_entry(signals, current_positions)
            if stagger_decision.action != 'hold':
                return stagger_decision

            # 4. 检查轮换交易机会
            rotation_decision = self._analyze_rotation_opportunities(signals, current_positions)
            if rotation_decision.action != 'hold':
                return rotation_decision

            # 5. 默认：选择最佳单一信号
            return self._select_best_single_signal(signals, current_positions)

        except Exception as e:
            self.logger.error(f"分析组合交易机会失败: {e}")
            return ComboDecision(
                action='hold',
                combo_trades=[],
                reason=f"分析失败: {e}",
                confidence=0.0,
                expected_return=0.0,
                max_risk=0.0
            )

    def _analyze_hedge_opportunities(self, signals: List[Dict], current_positions: Dict) -> ComboDecision:
        """分析对冲机会"""
        try:
            # 1. 检查是否已有活跃的对冲组合
            existing_hedge_combos = self._get_existing_hedge_combos(current_positions)
            if existing_hedge_combos:
                self.logger.info(f"已存在{len(existing_hedge_combos)}个对冲组合，跳过新的对冲创建")
                return ComboDecision(action='hold', combo_trades=[], reason="已存在对冲组合", confidence=0.0, expected_return=0.0, max_risk=0.0)

            # 2. 检查时间间隔限制
            if not self._check_combo_time_interval('HEDGE', min_interval_minutes=30):
                return ComboDecision(action='hold', combo_trades=[], reason="对冲组合创建间隔不足", confidence=0.0, expected_return=0.0, max_risk=0.0)

            # 3. 检查现有仓位是否需要对冲
            position_risk = self._calculate_position_risk(current_positions)

            # 如果有对冲信号且满足条件，优先创建对冲组合
            has_hedge_signals = any(
                len([s for s in signals if s['symbol'] in pair_symbols]) >= 2
                for pair_symbols in self.hedge_pairs.values()
            )

            # 降低对冲触发阈值，或者有明确的对冲信号时直接创建
            if position_risk > 0.3 or (has_hedge_signals and len(current_positions) == 0):  # 降低阈值或无持仓时有对冲信号
                hedge_combos = []

                for hedge_name, pair_symbols in self.hedge_pairs.items():
                    # 检查是否已有相同货币对组合
                    if self._has_existing_pair_combo(pair_symbols, current_positions):
                        self.logger.info(f"货币对组合{pair_symbols}已存在，跳过{hedge_name}")
                        continue

                    # 检查是否有对冲信号
                    hedge_signals = [s for s in signals if s['symbol'] in pair_symbols]

                    if len(hedge_signals) >= 2:
                        # 创建对冲组合
                        combo = ComboTrade(
                            combo_id=f"HEDGE_{hedge_name}_{datetime.now().strftime('%H%M%S')}",
                            combo_type=ComboType.HEDGE_PAIR,
                            symbols=[s['symbol'] for s in hedge_signals[:2]],
                            directions=['long', 'short'],  # 对冲方向
                            position_sizes=[0.03, 0.03],   # 较小仓位
                            entry_times=[datetime.now(), datetime.now() + timedelta(minutes=5)],
                            target_correlation=-0.5,
                            risk_level='low',
                            description=f"对冲组合：{hedge_name}"
                        )
                        hedge_combos.append(combo)

                        # 记录组合创建时间
                        self.last_combo_time['HEDGE'] = datetime.now()
                        self.active_combos[combo.combo_id] = combo
                        break  # 只创建一个对冲组合

                if hedge_combos:
                    return ComboDecision(
                        action='create_combo',
                        combo_trades=hedge_combos[:1],  # 只选择一个对冲组合
                        reason=f"现有仓位风险过高({position_risk:.1%})，创建对冲组合",
                        confidence=0.8,
                        expected_return=0.02,
                        max_risk=0.03
                    )

            return ComboDecision(action='hold', combo_trades=[], reason="无需对冲", confidence=0.0, expected_return=0.0, max_risk=0.0)

        except Exception as e:
            self.logger.error(f"分析对冲机会失败: {e}")
            return ComboDecision(action='hold', combo_trades=[], reason="对冲分析失败", confidence=0.0, expected_return=0.0, max_risk=0.0)

    def _analyze_arbitrage_opportunities(self, signals: List[Dict]) -> ComboDecision:
        """分析套利机会"""
        try:
            # 寻找强弱对比机会
            strong_signals = [s for s in signals if s.get('signal_strength', 0) > 0.5]
            weak_signals = [s for s in signals if s.get('signal_strength', 0) < -0.5]

            if strong_signals and weak_signals:
                # 选择最强和最弱的信号
                strongest = max(strong_signals, key=lambda x: x.get('signal_strength', 0))
                weakest = min(weak_signals, key=lambda x: x.get('signal_strength', 0))

                # 检查相关性
                correlation = self._get_correlation(strongest['symbol'], weakest['symbol'])

                if abs(correlation) < 0.3:  # 低相关性，适合套利
                    combo = ComboTrade(
                        combo_id=f"ARBITRAGE_{datetime.now().strftime('%H%M%S')}",
                        combo_type=ComboType.ARBITRAGE,
                        symbols=[strongest['symbol'], weakest['symbol']],
                        directions=['long', 'short'],
                        position_sizes=[0.04, 0.04],
                        entry_times=[datetime.now(), datetime.now() + timedelta(minutes=2)],
                        target_correlation=correlation,
                        risk_level='medium',
                        description=f"强弱套利：{strongest['symbol']} vs {weakest['symbol']}"
                    )

                    return ComboDecision(
                        action='create_combo',
                        combo_trades=[combo],
                        reason=f"强弱套利机会：相关性{correlation:.2f}",
                        confidence=0.7,
                        expected_return=0.04,
                        max_risk=0.06
                    )

            return ComboDecision(action='hold', combo_trades=[], reason="无套利机会", confidence=0.0, expected_return=0.0, max_risk=0.0)

        except Exception as e:
            self.logger.error(f"分析套利机会失败: {e}")
            return ComboDecision(action='hold', combo_trades=[], reason="套利分析失败", confidence=0.0, expected_return=0.0, max_risk=0.0)

    def _analyze_staggered_entry(self, signals: List[Dict], current_positions: Dict) -> ComboDecision:
        """分析分批建仓机会"""
        try:
            # 如果有多个同方向信号，考虑分批建仓
            long_signals = [s for s in signals if s.get('action') == 'enter_long']
            short_signals = [s for s in signals if s.get('action') == 'enter_short']

            if len(long_signals) >= 3:  # 3个以上多头信号
                # 选择前3个最强信号，分批建仓
                sorted_signals = sorted(long_signals, key=lambda x: x.get('confidence', 0), reverse=True)[:3]

                combo = ComboTrade(
                    combo_id=f"STAGGER_LONG_{datetime.now().strftime('%H%M%S')}",
                    combo_type=ComboType.STAGGERED,
                    symbols=[s['symbol'] for s in sorted_signals],
                    directions=['long', 'long', 'long'],
                    position_sizes=[0.04, 0.03, 0.03],  # 递减仓位
                    entry_times=[
                        datetime.now(),
                        datetime.now() + timedelta(minutes=15),
                        datetime.now() + timedelta(minutes=30)
                    ],
                    target_correlation=0.5,
                    risk_level='medium',
                    description="分批建仓：多头组合"
                )

                return ComboDecision(
                    action='create_combo',
                    combo_trades=[combo],
                    reason=f"分批建仓：{len(sorted_signals)}个多头信号",
                    confidence=0.6,
                    expected_return=0.06,
                    max_risk=0.10
                )

            return ComboDecision(action='hold', combo_trades=[], reason="无分批建仓机会", confidence=0.0, expected_return=0.0, max_risk=0.0)

        except Exception as e:
            self.logger.error(f"分析分批建仓失败: {e}")
            return ComboDecision(action='hold', combo_trades=[], reason="分批分析失败", confidence=0.0, expected_return=0.0, max_risk=0.0)

    def _analyze_rotation_opportunities(self, signals: List[Dict], current_positions: Dict) -> ComboDecision:
        """分析轮换交易机会"""
        try:
            # 如果当前持仓较多，考虑轮换
            if len(current_positions) >= 2:
                # 选择最佳新信号，但延迟执行
                if signals:
                    best_signal = max(signals, key=lambda x: x.get('confidence', 0))

                    combo = ComboTrade(
                        combo_id=f"ROTATION_{datetime.now().strftime('%H%M%S')}",
                        combo_type=ComboType.ROTATION,
                        symbols=[best_signal['symbol']],
                        directions=[best_signal.get('action', 'long').replace('enter_', '')],
                        position_sizes=[0.05],
                        entry_times=[datetime.now() + timedelta(minutes=20)],  # 延迟20分钟
                        target_correlation=0.0,
                        risk_level='low',
                        description=f"轮换交易：{best_signal['symbol']}"
                    )

                    return ComboDecision(
                        action='create_combo',
                        combo_trades=[combo],
                        reason="轮换交易：避免集中建仓",
                        confidence=0.5,
                        expected_return=0.03,
                        max_risk=0.05
                    )

            return ComboDecision(action='hold', combo_trades=[], reason="无轮换机会", confidence=0.0, expected_return=0.0, max_risk=0.0)

        except Exception as e:
            self.logger.error(f"分析轮换机会失败: {e}")
            return ComboDecision(action='hold', combo_trades=[], reason="轮换分析失败", confidence=0.0, expected_return=0.0, max_risk=0.0)

    def _select_best_single_signal(self, signals: List[Dict], current_positions: Dict) -> ComboDecision:
        """选择最佳单一信号"""
        try:
            if not signals:
                return ComboDecision(action='hold', combo_trades=[], reason="无可用信号", confidence=0.0, expected_return=0.0, max_risk=0.0)

            # 过滤掉与现有持仓高相关的信号
            filtered_signals = []
            for signal in signals:
                is_correlated = False
                for pos_symbol in current_positions.keys():
                    correlation = self._get_correlation(signal['symbol'], pos_symbol)
                    if abs(correlation) > 0.7:
                        is_correlated = True
                        break

                if not is_correlated:
                    filtered_signals.append(signal)

            if filtered_signals:
                best_signal = max(filtered_signals, key=lambda x: x.get('confidence', 0))

                combo = ComboTrade(
                    combo_id=f"SINGLE_{datetime.now().strftime('%H%M%S')}",
                    combo_type=ComboType.ROTATION,
                    symbols=[best_signal['symbol']],
                    directions=[best_signal.get('action', 'long').replace('enter_', '')],
                    position_sizes=[best_signal.get('position_size', 0.05)],
                    entry_times=[datetime.now()],
                    target_correlation=0.0,
                    risk_level='medium',
                    description=f"单一交易：{best_signal['symbol']}"
                )

                return ComboDecision(
                    action='create_combo',
                    combo_trades=[combo],
                    reason=f"最佳单一信号：{best_signal['symbol']}",
                    confidence=best_signal.get('confidence', 0.6),
                    expected_return=0.04,
                    max_risk=0.05
                )

            return ComboDecision(action='hold', combo_trades=[], reason="所有信号与现有持仓高相关", confidence=0.0, expected_return=0.0, max_risk=0.0)

        except Exception as e:
            self.logger.error(f"选择最佳信号失败: {e}")
            return ComboDecision(action='hold', combo_trades=[], reason="信号选择失败", confidence=0.0, expected_return=0.0, max_risk=0.0)

    def _get_existing_hedge_combos(self, current_positions: Dict) -> List[str]:
        """获取现有的对冲组合"""
        try:
            hedge_combos = []

            # 检查是否有对冲货币对组合
            for hedge_name, pair_symbols in self.hedge_pairs.items():
                has_all_pairs = True
                for symbol in pair_symbols:
                    if symbol not in current_positions:
                        has_all_pairs = False
                        break

                if has_all_pairs:
                    hedge_combos.append(hedge_name)

            return hedge_combos
        except Exception as e:
            self.logger.error(f"获取现有对冲组合失败: {e}")
            return []

    def _check_combo_time_interval(self, combo_type: str, min_interval_minutes: int) -> bool:
        """检查组合交易时间间隔"""
        try:
            if combo_type not in self.last_combo_time:
                return True

            last_time = self.last_combo_time[combo_type]
            time_diff = (datetime.now() - last_time).total_seconds() / 60

            return time_diff >= min_interval_minutes
        except Exception as e:
            self.logger.error(f"检查时间间隔失败: {e}")
            return True

    def _has_existing_pair_combo(self, pair_symbols: List[str], current_positions: Dict) -> bool:
        """检查是否已有相同货币对组合"""
        try:
            # 检查是否所有货币对都有持仓
            existing_count = 0
            for symbol in pair_symbols:
                if symbol in current_positions:
                    existing_count += 1

            # 如果超过一半的货币对已有持仓，认为组合已存在
            return existing_count >= len(pair_symbols) / 2
        except Exception as e:
            self.logger.error(f"检查货币对组合失败: {e}")
            return False

    def _get_correlation(self, symbol1: str, symbol2: str) -> float:
        """获取两个货币对的相关性"""
        try:
            if symbol1 in self.correlation_matrix and symbol2 in self.correlation_matrix[symbol1]:
                return self.correlation_matrix[symbol1][symbol2]
            elif symbol2 in self.correlation_matrix and symbol1 in self.correlation_matrix[symbol2]:
                return self.correlation_matrix[symbol2][symbol1]
            else:
                return 0.0
        except:
            return 0.0

    def _calculate_position_risk(self, current_positions: Dict) -> float:
        """计算当前持仓风险"""
        try:
            if not current_positions:
                return 0.0

            # 简化风险计算：基于持仓数量和方向集中度
            total_positions = len(current_positions)
            same_direction_count = 0

            # 假设所有持仓都是多头（实际应该从持仓数据获取）
            same_direction_count = total_positions

            # 风险评分
            risk_score = (total_positions / 7.0) * 0.5 + (same_direction_count / total_positions) * 0.5

            return min(1.0, risk_score)

        except:
            return 0.0

# 创建全局实例
combo_trading_manager = ComboTradingManager()
