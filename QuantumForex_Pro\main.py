"""
QuantumForex Pro - 主程序入口
世界顶级量化交易系统启动器
"""

import sys
import os
import time
import threading
import signal
from datetime import datetime, timedelta
import traceback
import pandas as pd
from typing import Dict, List, Tuple, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.config import config
from memory_optimizer import memory_optimizer, emergency_memory_cleanup, optimize_system_memory
from core.signal_engine.simple_technical_analyzer import SimpleTechnicalAnalyzer
from core.signal_engine.signal_fusion_engine import SignalFusionEngine
from core.ml_engine.lightweight_ml_engine import LightweightMLEngine
from core.risk_engine.advanced_risk_manager import AdvancedRiskManager
from core.execution_engine.trade_executor import TradeExecutor
from core.position_manager import PositionManager, LLMPositionAnalyzer
from core.system_coordinator import SystemCoordinator, SystemPriority, SystemState
from strategies.master_strategy_engine import MasterStrategyEngine
from core.learning_system import LearningCoordinator
from core.mt4_integration import MT4TradeMonitor

class QuantumForexPro:
    """
    QuantumForex Pro 主系统
    世界顶级的量化交易系统
    """

    def __init__(self):
        self.config = config
        self.running = False
        self.system_start_time = datetime.now()

        # 初始化核心引擎
        print("🚀 QuantumForex Pro 正在启动...")
        print("=" * 60)

        try:
            # 1. 技术分析引擎
            print("📊 初始化技术分析引擎...")
            self.technical_analyzer = SimpleTechnicalAnalyzer()
            print("✅ 技术分析引擎初始化完成")

            # 2. 信号融合引擎
            print("🔄 初始化信号融合引擎...")
            self.signal_fusion = SignalFusionEngine()
            print("✅ 信号融合引擎初始化完成")

            # 3. 机器学习引擎
            print("🤖 初始化机器学习引擎...")
            self.ml_engine = LightweightMLEngine()
            print("✅ 机器学习引擎初始化完成")

            # 4. 风险管理引擎
            print("🛡️ 初始化风险管理引擎...")
            self.risk_manager = AdvancedRiskManager()
            print("✅ 风险管理引擎初始化完成")

            # 5. 交易执行引擎
            print("⚡ 初始化交易执行引擎...")
            self.trade_executor = TradeExecutor()
            print("✅ 交易执行引擎初始化完成")

            # 6. 同步MT4持仓数据
            print("🔄 同步MT4持仓数据...")
            self.trade_executor._print_current_positions()
            print("✅ MT4持仓数据同步完成")

            # 7. 初始化持仓管理器
            print("🎯 初始化智能持仓管理器...")
            self.position_manager = PositionManager(self.trade_executor)
            self.llm_analyzer = LLMPositionAnalyzer()
            print("✅ 智能持仓管理器初始化完成")

            # 8. 初始化系统协调器
            print("🎛️ 初始化系统协调器...")
            self.system_coordinator = SystemCoordinator()
            print("✅ 系统协调器初始化完成")

            # 9. 初始化主策略引擎
            print("🧠 初始化主策略引擎...")
            self.master_strategy = MasterStrategyEngine()
            print("✅ 主策略引擎初始化完成")

            # 10. 初始化学习系统
            print("📚 初始化学习系统...")
            self.learning_coordinator = LearningCoordinator()
            self.learning_coordinator.start()

            # 注册参数更新回调
            self.learning_coordinator.register_parameter_update_callback(self._on_parameters_updated)
            print("✅ 学习系统初始化完成")

            # 11. 初始化MT4交易监控
            print("🔗 初始化MT4交易监控...")
            self.mt4_monitor = MT4TradeMonitor(self.learning_coordinator)
            self.mt4_monitor.start_monitoring()
            print("✅ MT4交易监控初始化完成")

            # 系统状态
            self.system_stats = {
                'total_signals': 0,
                'successful_analyses': 0,
                'failed_analyses': 0,
                'uptime': 0,
                'last_analysis_time': None,
                'performance_metrics': {}
            }

            # 12. 启动内存监控
            print("🔍 启动内存监控...")
            memory_optimizer.start_monitoring()
            print("✅ 内存监控启动完成")

            print("=" * 60)
            print("🎯 QuantumForex Pro 启动完成!")
            print(f"📅 启动时间: {self.system_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)

        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            print(traceback.format_exc())
            sys.exit(1)

    def start(self):
        """启动系统"""
        try:
            self.running = True

            # 设置信号处理
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)

            print("🔄 QuantumForex Pro 开始运行...")
            print("按 Ctrl+C 停止系统")
            print("-" * 60)

            # 启动主循环
            self._main_loop()

        except KeyboardInterrupt:
            print("\n⏹️ 收到停止信号...")
        except Exception as e:
            print(f"❌ 系统运行错误: {e}")
            print(traceback.format_exc())
        finally:
            self._shutdown()

    def _main_loop(self):
        """主执行循环"""
        last_analysis_time = datetime.now() - timedelta(minutes=5)  # 确保首次运行

        while self.running:
            try:
                current_time = datetime.now()

                # 检查是否需要进行分析
                if self._should_run_analysis(current_time, last_analysis_time):
                    print(f"\n🔍 开始分析 - {current_time.strftime('%H:%M:%S')}")

                    # 执行分析循环
                    success = self._execute_analysis_cycle()

                    if success:
                        self.system_stats['successful_analyses'] += 1
                        last_analysis_time = current_time
                        print("✅ 分析完成")
                    else:
                        self.system_stats['failed_analyses'] += 1
                        print("❌ 分析失败")

                    self.system_stats['last_analysis_time'] = current_time

                # 更新系统统计
                self._update_system_stats()

                # 显示系统状态
                if current_time.minute % 5 == 0 and current_time.second < 10:
                    self._display_system_status()

                # 短暂休眠
                time.sleep(10)  # 每10秒检查一次

            except Exception as e:
                print(f"❌ 主循环错误: {e}")
                self.system_stats['failed_analyses'] += 1
                time.sleep(30)  # 错误后等待30秒

    def _should_run_analysis(self, current_time: datetime, last_analysis_time: datetime) -> bool:
        """判断是否应该运行分析"""
        try:
            # 检查时间间隔 - 改为更频繁的分析
            time_diff = (current_time - last_analysis_time).total_seconds()
            min_interval = 60  # 改为1分钟间隔，更接近实时

            if time_diff < min_interval:
                return False

            # 检查市场时间
            if not self._is_market_open(current_time):
                return False

            # 检查系统状态
            if not self._is_system_healthy():
                return False

            return True

        except Exception:
            return False

    def _is_market_open(self, current_time: datetime) -> bool:
        """检查市场是否开放"""
        try:
            # 简化的市场时间检查
            # 外汇市场周一到周五24小时开放
            weekday = current_time.weekday()

            # 周末不交易
            if weekday >= 5:  # 周六、周日
                return False

            # 周五晚上到周一早上不交易
            if weekday == 4 and current_time.hour >= 22:  # 周五22点后
                return False

            if weekday == 0 and current_time.hour < 6:  # 周一6点前
                return False

            return True

        except Exception:
            return True  # 默认认为市场开放

    def _is_system_healthy(self) -> bool:
        """检查系统健康状态"""
        try:
            # 检查内存使用
            import psutil
            memory_usage = psutil.virtual_memory().percent / 100

            # 分级内存管理策略
            if memory_usage > self.config.SYSTEM_CONFIG.get('critical_memory_usage', 0.90):
                print(f"🚨 内存使用危险: {memory_usage:.1%} - 系统将停止运行")
                emergency_memory_cleanup()
                return False

            elif memory_usage > self.config.SYSTEM_CONFIG['max_memory_usage']:
                print(f"⚠️ 内存使用过高: {memory_usage:.1%} - 执行清理但继续运行")
                emergency_memory_cleanup()
                # 不返回False，让系统继续运行但会执行清理

            elif memory_usage > self.config.SYSTEM_CONFIG.get('warning_memory_usage', 0.75):
                print(f"📊 内存使用警告: {memory_usage:.1%}")

            # 检查CPU使用
            cpu_usage = psutil.cpu_percent(interval=1) / 100

            if cpu_usage > self.config.SYSTEM_CONFIG['max_cpu_usage']:
                print(f"⚠️ CPU使用过高: {cpu_usage:.1%}")
                # CPU高也不停止系统，只是警告

            return True  # 除非内存达到危险级别，否则总是返回True

        except Exception:
            return True  # 如果无法检查，默认认为健康

    def _execute_analysis_cycle(self) -> bool:
        """执行分析周期 - 优化后的统一流程"""
        try:
            # 1. 获取市场数据
            print("📈 获取市场数据...")
            market_data = self._get_market_data()

            if not market_data:
                print("❌ 无法获取市场数据")
                return False

            # 2. 获取当前持仓信息（统一从MT4获取）
            print("📊 获取当前持仓信息...")
            current_positions = self._get_current_positions_unified()

            # 3. 执行统一风险评估（避免重复评估）
            print("🛡️ 执行风险评估...")
            risk_assessment = self._execute_unified_risk_assessment(current_positions, market_data)

            # 4. 使用主策略引擎生成决策（集成风险评估结果）
            print("🧠 执行主策略分析...")
            master_decision = self.master_strategy.generate_master_decision(
                market_data, current_positions, risk_assessment
            )

            print(f"📋 策略决策: {master_decision.strategy_used.value}")
            print(f"🎯 市场条件: {master_decision.market_condition.value}")
            print(f"💪 置信度: {master_decision.confidence:.2f}")
            print(f"📝 决策数量: {len(master_decision.decisions)}")
            print(f"💡 决策说明: {master_decision.reasoning}")

            # 5. 转换为系统可执行的交易决策
            trading_decisions = self._convert_master_decisions_to_trades(master_decision.decisions)

            # 6. 通过系统协调器执行智能持仓管理
            print("🎯 提交持仓管理任务...")
            position_task_id = self.system_coordinator.submit_task(
                system_name='position_manager',
                action='analyze_positions',
                data={'positions': 'current'},
                priority=SystemPriority.HIGH
            )

            if position_task_id:
                self._perform_position_management()

            # 7. 通过系统协调器执行交易决策
            risk_metrics = risk_assessment.get('risk_metrics')
            should_execute = (trading_decisions and
                            risk_metrics and
                            risk_metrics.recommended_action.value in ['allow_trading', 'reduce_position'])

            if should_execute:
                # 提交交易任务到协调器
                trade_task_id = self.system_coordinator.submit_task(
                    system_name='trading_system',
                    action='execute_trade',
                    data={'decisions': trading_decisions},
                    priority=SystemPriority.MEDIUM
                )

                if trade_task_id:
                    print("⚡ 执行交易决策...")

                    # 将风险评估传递给交易执行引擎
                    self.trade_executor.set_risk_assessment(risk_assessment)

                    executed_orders = self.trade_executor.execute_trading_decisions(trading_decisions)
                    print(f"📊 执行结果: {len(executed_orders)}/{len(trading_decisions)} 订单成功")

                    # 记录交易到学习系统
                    if executed_orders:
                        self._record_trade_execution(trading_decisions)
                else:
                    print("⏸️ 交易任务被协调器拒绝")
            else:
                # 详细分析拒绝交易的原因
                self._analyze_trading_rejection(trading_decisions, risk_assessment)

            # 7. 显示结果
            self._display_master_decision_results(master_decision, trading_decisions)

            # 8. 更新统计
            self.system_stats['total_signals'] += len(master_decision.decisions)
            self.system_stats['successful_analyses'] += 1

            # 9. 显示系统协调器状态
            coordinator_status = self.system_coordinator.get_system_status()
            print(f"🎛️ 系统状态: {coordinator_status['current_state']} | "
                  f"活跃任务: {coordinator_status['active_tasks']} | "
                  f"队列: {coordinator_status['queue_size']}")

            # 10. 显示MT4监控状态
            mt4_status = self.mt4_monitor.get_monitoring_status()
            print(f"🔗 MT4监控: {'运行中' if mt4_status['monitoring'] else '已停止'} | "
                  f"运行时间: {mt4_status['uptime']} | "
                  f"健康评分: {mt4_status['system_health']:.1%}")

            return True

        except Exception as e:
            print(f"❌ 分析周期执行失败: {e}")
            return False

    def _get_market_data(self) -> Dict:
        """获取真实市场数据"""
        try:
            print("🔗 连接真实数据源...")

            # 导入真实数据获取模块
            import sys
            import os

            # 使用本地模块而不是外部app
            from utils.db_client import get_connection, execute_query
            from utils.mt4_client import MT4Client

            market_data = {}

            # 获取真实的EURUSD数据
            market_data['EURUSD'] = self._get_real_eurusd_data()

            # 获取所有支持的货币对数据（移除黄金交易，保留7个货币对）
            other_symbols = ['GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCHF', 'USDCAD', 'USDJPY']

            for symbol in other_symbols:
                try:
                    real_data = self._get_real_symbol_data(symbol)
                    if real_data:
                        market_data[symbol] = real_data
                        print(f"✅ 成功获取{symbol}真实数据")
                    else:
                        # 如果获取失败，使用基于EURUSD的相关数据
                        market_data[symbol] = self._generate_correlated_data(market_data.get('EURUSD'), symbol)
                        print(f"📊 生成{symbol}相关数据")
                except Exception as e:
                    print(f"⚠️ 获取{symbol}数据失败: {e}")
                    # 如果获取失败，使用基于EURUSD的相关数据
                    market_data[symbol] = self._generate_correlated_data(market_data.get('EURUSD'), symbol)

            print(f"✅ 成功获取{len(market_data)}个货币对的真实数据")
            return market_data

        except Exception as e:
            print(f"❌ 真实数据获取失败: {e}")
            print("🔄 回退到增强模拟数据...")
            return self._get_fallback_data()

    def _get_real_eurusd_data(self) -> Dict:
        """获取真实的EURUSD数据"""
        try:
            from utils.db_client import execute_query

            # 首先检查数据库中的数据量
            count_sql = "SELECT COUNT(*) as total FROM min_quote_eurusd"
            count_result = execute_query(count_sql)
            total_records = count_result[0]['total'] if count_result else 0
            print(f"📊 数据库中EURUSD总记录数: {total_records}")

            # 获取更多历史数据用于ML训练 - 增加到500条
            data_limit = min(500, total_records)  # 最多500条，但不超过总数

            sql = """
            SELECT time_date_str, price, min as low_price, max as high_price, volume, create_time
            FROM min_quote_eurusd
            ORDER BY time_min_int DESC
            LIMIT %s
            """ % data_limit

            print(f"📈 获取最近{data_limit}条EURUSD数据...")

            raw_data = execute_query(sql)

            if not raw_data:
                raise Exception("数据库无数据")

            # 转换为DataFrame
            import pandas as pd
            import numpy as np

            df_data = []
            for row in reversed(raw_data):  # 反转以获得时间顺序
                try:
                    # 解析时间
                    time_str = row['time_date_str']
                    timestamp = pd.to_datetime(time_str)

                    # 价格数据
                    close_price = float(row['price'])
                    high_price = float(row['high_price']) if row['high_price'] else close_price
                    low_price = float(row['low_price']) if row['low_price'] else close_price
                    volume = int(row['volume']) if row['volume'] else 1000

                    # 估算开盘价（使用前一个收盘价或当前收盘价）
                    open_price = close_price

                    df_data.append({
                        'timestamp': timestamp,
                        'open': open_price,
                        'high': max(high_price, close_price),
                        'low': min(low_price, close_price),
                        'close': close_price,
                        'volume': volume
                    })

                except Exception as e:
                    print(f"⚠️ 解析数据行失败: {e}")
                    continue

            if not df_data:
                raise Exception("无有效数据")

            # 创建DataFrame
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)

            # 修正开盘价
            df['open'] = df['close'].shift(1).fillna(df['close'])

            # 计算统计信息
            current_price = df['close'].iloc[-1]
            price_changes = df['close'].pct_change().dropna()
            volatility = price_changes.std() if len(price_changes) > 1 else 0.001

            print(f"✅ EURUSD真实数据: {len(df)}条记录, 当前价格: {current_price:.5f}")

            # 检查数据质量
            if len(df) >= 200:
                print("🎯 数据量充足，ML模型可以正常训练")
            elif len(df) >= 100:
                print("⚠️ 数据量中等，ML模型可以基础训练")
            else:
                print("❌ 数据量不足，建议获取更多历史数据")

            return {
                'ohlcv': df,
                'current_price': current_price,
                'volatility': volatility,
                'volume': df['volume'].iloc[-1],
                'data_source': 'pizza_quotes_db',
                'data_quality': 'high' if len(df) >= 200 else 'medium' if len(df) >= 100 else 'low'
            }

        except Exception as e:
            print(f"❌ 获取EURUSD真实数据失败: {e}")
            raise

    def _get_real_symbol_data(self, symbol: str) -> Optional[Dict]:
        """尝试获取其他货币对的真实数据"""
        try:
            # 尝试从MT4获取实时数据
            from utils.mt4_client import MT4Client

            mt4_client = MT4Client()
            if mt4_client.connect():
                market_info = mt4_client.get_market_info(symbol)
                if market_info and market_info.get('status') == 'success':
                    data = market_info.get('data', {})
                    current_price = (float(data.get('bid', 0)) + float(data.get('ask', 0))) / 2

                    if current_price > 0:
                        # 生成基于实时价格的历史数据
                        return self._generate_realistic_data(symbol, current_price)

            return None

        except Exception as e:
            print(f"⚠️ 从MT4获取{symbol}数据失败: {e}")
            return None

    def _generate_correlated_data(self, eurusd_data: Optional[Dict], symbol: str) -> Dict:
        """基于EURUSD数据生成相关货币对数据"""
        try:
            if not eurusd_data or 'ohlcv' not in eurusd_data:
                return self._generate_realistic_data(symbol, 1.0000)

            import pandas as pd
            import numpy as np

            eurusd_df = eurusd_data['ohlcv']

            # 货币对相关性系数（移除黄金，保留7个外汇货币对）
            correlation_map = {
                'GBPUSD': 0.85,   # 与EURUSD高度正相关
                'AUDUSD': 0.75,   # 与EURUSD中度正相关
                'NZDUSD': 0.70,   # 与EURUSD中度正相关
                'USDCHF': -0.80,  # 与EURUSD负相关
                'USDCAD': -0.65,  # 与EURUSD中度负相关
                'USDJPY': -0.45   # 与EURUSD弱负相关
            }

            correlation = correlation_map.get(symbol, 0.5)

            # 基础价格（移除黄金，保留7个外汇货币对）
            base_prices = {
                'GBPUSD': 1.2500,
                'AUDUSD': 0.6500,
                'NZDUSD': 0.6000,
                'USDCHF': 0.9200,
                'USDCAD': 1.3500,
                'USDJPY': 150.00
            }

            base_price = base_prices.get(symbol, 1.0000)

            # 计算EURUSD的收益率
            eurusd_returns = eurusd_df['close'].pct_change().fillna(0)

            # 生成相关的收益率
            np.random.seed(42)  # 确保可重复性
            random_component = np.random.normal(0, 0.0005, len(eurusd_returns))
            correlated_returns = correlation * eurusd_returns + np.sqrt(1 - correlation**2) * random_component

            # 生成价格序列
            prices = [base_price]
            for ret in correlated_returns[1:]:  # 跳过第一个NaN
                prices.append(prices[-1] * (1 + ret))

            # 创建OHLCV数据
            df_data = []
            for i, (timestamp, price) in enumerate(zip(eurusd_df.index, prices)):
                spread = price * 0.0002  # 0.02%的价差

                df_data.append({
                    'open': price,
                    'high': price + spread,
                    'low': price - spread,
                    'close': price,
                    'volume': np.random.randint(800, 1200)
                })

            df = pd.DataFrame(df_data, index=eurusd_df.index)

            current_price = df['close'].iloc[-1]
            volatility = correlated_returns.std()

            print(f"✅ {symbol}相关数据: 当前价格: {current_price:.5f}, 相关性: {correlation}")

            return {
                'ohlcv': df,
                'current_price': current_price,
                'volatility': volatility,
                'volume': df['volume'].iloc[-1],
                'data_source': 'correlated_simulation'
            }

        except Exception as e:
            print(f"❌ 生成{symbol}相关数据失败: {e}")
            return self._generate_realistic_data(symbol, 1.0000)

    def _generate_realistic_data(self, symbol: str, current_price: float) -> Dict:
        """生成基于实时价格的现实数据"""
        try:
            import pandas as pd
            import numpy as np

            # 生成100个数据点
            periods = 100
            dates = pd.date_range(end=datetime.now(), periods=periods, freq='1min')

            # 从当前价格向前推算历史价格
            np.random.seed(hash(symbol) % 1000)  # 基于符号的种子

            # 价格波动参数
            volatility = 0.0008  # 日波动率
            drift = 0.0001  # 微小趋势

            # 生成价格路径
            returns = np.random.normal(drift, volatility, periods)
            prices = [current_price]

            # 反向生成历史价格
            for i in range(periods - 1):
                prev_price = prices[-1] / (1 + returns[periods - 1 - i])
                prices.append(prev_price)

            prices.reverse()  # 恢复时间顺序

            # 创建OHLCV数据
            df_data = []
            for i, (timestamp, price) in enumerate(zip(dates, prices)):
                spread = price * 0.0003  # 价差

                df_data.append({
                    'open': price,
                    'high': price + np.random.uniform(0, spread),
                    'low': price - np.random.uniform(0, spread),
                    'close': price,
                    'volume': np.random.randint(500, 1500)
                })

            df = pd.DataFrame(df_data, index=dates)

            print(f"✅ {symbol}现实数据: 当前价格: {current_price:.5f}")

            return {
                'ohlcv': df,
                'current_price': current_price,
                'volatility': volatility,
                'volume': df['volume'].iloc[-1],
                'data_source': 'realistic_simulation'
            }

        except Exception as e:
            print(f"❌ 生成{symbol}现实数据失败: {e}")
            return {}

    def _get_fallback_data(self) -> Dict:
        """回退数据生成"""
        try:
            import pandas as pd
            import numpy as np

            print("🔄 使用增强模拟数据...")

            market_data = {}

            # 为所有支持的货币对生成增强模拟数据（移除黄金，保留7个外汇货币对）
            for symbol in self.config.TRADING_CONFIG['supported_pairs']:  # 支持7个外汇货币对
                # 生成模拟OHLCV数据
                dates = pd.date_range(end=datetime.now(), periods=100, freq='1min')

                # 基础价格（移除黄金，保留7个外汇货币对）
                base_prices = {
                    'EURUSD': 1.0850,
                    'GBPUSD': 1.2650,
                    'AUDUSD': 0.6420,
                    'NZDUSD': 0.6100,
                    'USDCHF': 0.9180,
                    'USDCAD': 1.3650,
                    'USDJPY': 149.50
                }

                base_price = base_prices.get(symbol, 1.0000)

                # 添加趋势成分（基于货币对特性）
                trend = np.linspace(0, 0.005, 100)  # 默认上升趋势

                # 根据货币对特性调整趋势
                if symbol in ['USDCHF', 'USDCAD']:  # USD强势货币对下降趋势
                    trend = -trend
                elif symbol in ['USDJPY']:  # 日元相关，波动较大
                    trend = trend * 2
                elif symbol in ['NZDUSD', 'AUDUSD']:  # 商品货币，波动适中
                    trend = trend * 1.2

                # 随机波动 + 趋势
                price_changes = np.random.normal(0, 0.0008, 100) + np.diff(np.concatenate([[0], trend]))
                prices = base_price + np.cumsum(price_changes)

                ohlcv_data = pd.DataFrame({
                    'open': prices + np.random.normal(0, 0.0001, 100),
                    'high': prices + np.abs(np.random.normal(0, 0.0005, 100)),
                    'low': prices - np.abs(np.random.normal(0, 0.0005, 100)),
                    'close': prices,
                    'volume': np.random.randint(1000, 10000, 100)
                }, index=dates)

                market_data[symbol] = {
                    'ohlcv': ohlcv_data,
                    'current_price': prices[-1],
                    'volatility': np.std(price_changes),
                    'volume': ohlcv_data['volume'].iloc[-1],
                    'data_source': 'enhanced_simulation'
                }

            print(f"✅ 生成{len(market_data)}个货币对的增强模拟数据")
            return market_data

        except Exception as e:
            print(f"❌ 回退数据生成失败: {e}")
            return {}

    def _select_currency_pairs(self, market_data: Dict) -> List[str]:
        """选择要分析的货币对（支持组合交易）"""
        try:
            available_pairs = list(market_data.keys())

            # 使用智能货币对选择器进行组合分析
            try:
                # 导入智能选择器
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from utils.intelligent_pair_selector import IntelligentPairSelector

                selector = IntelligentPairSelector()
                # 基于可用数据选择最优组合
                selected_pairs = []

                # 确保选择器有可用数据
                for pair in available_pairs:
                    if pair in selector.supported_symbols:
                        selected_pairs.append(pair)

                # 如果有多个可用货币对，使用智能选择
                if len(selected_pairs) >= 3:
                    # 选择3-4个货币对进行组合分析（平衡分析质量和计算成本）
                    optimal_pairs = selector.select_optimal_currency_pairs()
                    # 过滤出实际可用的货币对
                    final_selection = [pair for pair in optimal_pairs if pair in available_pairs]

                    if len(final_selection) >= 2:
                        print(f"🎯 智能选择货币对组合: {final_selection}")
                        return final_selection[:4]  # 最多4个货币对进行组合分析

            except Exception as e:
                print(f"⚠️ 智能选择器失败，使用默认选择: {e}")

            # 回退到默认选择逻辑：选择主要货币对进行组合分析
            priority_pairs = ['EURUSD', 'GBPUSD', 'AUDUSD', 'USDCHF', 'USDJPY']

            selected = []
            for pair in priority_pairs:
                if pair in available_pairs and len(selected) < 3:
                    selected.append(pair)

            # 如果主要货币对不足，添加其他可用货币对
            if len(selected) < 3:
                for pair in available_pairs:
                    if pair not in selected and len(selected) < 3:
                        selected.append(pair)

            print(f"🎯 选择货币对组合: {selected}")
            return selected if selected else ['EURUSD']  # 确保至少有一个货币对

        except Exception as e:
            print(f"❌ 货币对选择失败: {e}")
            return ['EURUSD']  # 默认返回欧元美元

    def _get_current_positions_unified(self) -> Dict:
        """统一获取当前持仓信息"""
        try:
            # 直接从交易执行器获取MT4真实持仓
            mt4_positions = self.trade_executor._get_mt4_real_positions()

            # 转换为策略引擎需要的格式
            positions_by_symbol = {}
            total_positions = len(mt4_positions)
            total_profit = sum(pos.get('profit', 0) for pos in mt4_positions)

            for pos in mt4_positions:
                symbol = pos.get('symbol', 'UNKNOWN')
                if symbol not in positions_by_symbol:
                    positions_by_symbol[symbol] = []
                positions_by_symbol[symbol].append(pos)

            return {
                'positions_by_symbol': positions_by_symbol,
                'total_positions': total_positions,
                'total_profit': total_profit,
                'raw_positions': mt4_positions
            }

        except Exception as e:
            print(f"❌ 获取统一持仓信息失败: {e}")
            return {
                'positions_by_symbol': {},
                'total_positions': 0,
                'total_profit': 0.0,
                'raw_positions': []
            }

    def _execute_unified_risk_assessment(self, current_positions: Dict, market_data: Dict) -> Dict:
        """执行统一风险评估"""
        try:
            # 构建账户信息
            account_info = {
                'balance': 10000,  # 模拟账户余额
                'equity': 10000 + current_positions.get('total_profit', 0),
                'initial_balance': 10000,
                'daily_pnl': current_positions.get('total_profit', 0),
                'margin_used': current_positions.get('total_positions', 0) * 100,  # 简化计算
                'margin_free': 10000 - (current_positions.get('total_positions', 0) * 100),
                'margin_level': 1000 if current_positions.get('total_positions', 0) == 0 else
                              (10000 / (current_positions.get('total_positions', 0) * 100)) * 100
            }

            # 构建持仓信息
            positions = current_positions.get('raw_positions', [])

            # 构建市场数据
            market_risk_data = {
                'volatility': max([data.get('volatility', 0.01) for data in market_data.values()]),
                'sentiment': 'NEUTRAL',
                'liquidity_risk': 0.0
            }

            # 执行风险评估
            risk_metrics = self.risk_manager.assess_comprehensive_risk(
                account_info, positions, market_risk_data
            )

            return {
                'risk_metrics': risk_metrics,
                'account_info': account_info,
                'positions': positions,
                'market_risk_data': market_risk_data
            }

        except Exception as e:
            print(f"❌ 统一风险评估失败: {e}")
            # 返回默认的安全风险评估
            from core.risk_engine.advanced_risk_manager import RiskLevel, RiskAction

            class DefaultRiskMetrics:
                def __init__(self):
                    self.risk_level = RiskLevel.MEDIUM
                    self.risk_score = 0.5
                    self.recommended_action = RiskAction.ALLOW_TRADING

            return {
                'risk_metrics': DefaultRiskMetrics(),
                'account_info': {'balance': 10000, 'equity': 10000},
                'positions': [],
                'market_risk_data': {'volatility': 0.01}
            }

    def _perform_risk_assessment(self, analysis_results: Dict) -> Dict:
        """执行风险评估"""
        try:
            # 模拟账户信息
            account_info = {
                'balance': 10000,
                'equity': 10000,
                'initial_balance': 10000,
                'daily_pnl': 0,
                'margin_used': 0,
                'margin_free': 10000,
                'margin_level': 1000
            }

            # 模拟持仓信息
            positions = []

            # 模拟市场数据
            market_data = {
                'volatility': 0.01,
                'sentiment': 'NEUTRAL',
                'liquidity_risk': 0.0
            }

            # 执行风险评估
            risk_metrics = self.risk_manager.assess_comprehensive_risk(
                account_info, positions, market_data
            )

            return {
                'risk_metrics': risk_metrics,
                'account_info': account_info,
                'positions': positions
            }

        except Exception as e:
            print(f"❌ 风险评估失败: {e}")
            return {}

    def _generate_trading_decisions(self, analysis_results: Dict, risk_assessment: Dict) -> List[Dict]:
        """生成交易决策"""
        try:
            decisions = []

            risk_metrics = risk_assessment.get('risk_metrics')
            if not risk_metrics:
                return decisions

            # 检查是否允许交易
            if risk_metrics.recommended_action.value != 'allow_trading':
                print(f"🚫 风险管理建议: {risk_metrics.recommended_action.value}")
                return decisions

            # 为每个分析结果生成决策
            for symbol, analysis in analysis_results.items():
                try:
                    fused_signal = analysis.get('fused_signal')
                    if not fused_signal:
                        continue

                    # 检查信号质量 - 进一步降低阈值，更容易产生交易
                    # 只排除VERY_LOW质量的信号，接受LOW及以上
                    if fused_signal.signal_quality == 'VERY_LOW':
                        continue

                    # 检查信号强度 - 进一步降低阈值从0.3到0.2
                    if fused_signal.strength < 0.2:
                        continue

                    # 检查信号方向 - 只接受明确的买卖信号
                    if fused_signal.direction.value == 0:  # NEUTRAL
                        continue

                    # 生成交易决策
                    decision = {
                        'symbol': symbol,
                        'action': fused_signal.direction.name,
                        'entry_price': fused_signal.entry_price,
                        'stop_loss': fused_signal.stop_loss,
                        'take_profit': fused_signal.take_profit,
                        'risk_reward_ratio': fused_signal.risk_reward_ratio,
                        'confidence': fused_signal.confidence,
                        'strength': fused_signal.strength,
                        'timestamp': datetime.now().isoformat()
                    }

                    decisions.append(decision)

                except Exception as e:
                    print(f"❌ {symbol} 决策生成失败: {e}")
                    continue

            return decisions

        except Exception as e:
            print(f"❌ 交易决策生成失败: {e}")
            return []

    def _display_analysis_results(self, analysis_results: Dict, risk_assessment: Dict,
                                trading_decisions: List[Dict]):
        """显示分析结果"""
        try:
            print("\n" + "="*60)
            print("📊 分析结果摘要")
            print("="*60)

            # 显示分析的货币对
            print(f"🎯 分析货币对: {', '.join(analysis_results.keys())}")

            # 显示风险状态
            risk_metrics = risk_assessment.get('risk_metrics')
            if risk_metrics:
                print(f"🛡️ 风险等级: {risk_metrics.risk_level.name}")
                print(f"📈 风险评分: {risk_metrics.risk_score:.2f}")
                print(f"💡 建议行动: {risk_metrics.recommended_action.value}")

            # 显示交易决策
            if trading_decisions:
                print(f"\n💰 交易信号 ({len(trading_decisions)}个):")
                for i, decision in enumerate(trading_decisions, 1):
                    print(f"  {i}. {decision['symbol']}: {decision['action']}")
                    print(f"     入场: {decision['entry_price']:.5f}")
                    print(f"     止损: {decision['stop_loss']:.5f}")
                    print(f"     止盈: {decision['take_profit']:.5f}")
                    print(f"     置信度: {decision['confidence']:.2%}")
                    print(f"     强度: {decision['strength']:.2%}")
            else:
                print("\n💰 交易信号: 无")

            print("="*60)

        except Exception as e:
            print(f"❌ 结果显示失败: {e}")

    def _display_system_status(self):
        """显示系统状态"""
        try:
            current_time = datetime.now()
            uptime = current_time - self.system_start_time

            print(f"\n📊 系统状态 - {current_time.strftime('%H:%M:%S')}")
            print("-" * 40)
            print(f"⏱️ 运行时间: {str(uptime).split('.')[0]}")
            print(f"📈 成功分析: {self.system_stats['successful_analyses']}")
            print(f"❌ 失败分析: {self.system_stats['failed_analyses']}")
            print(f"📊 总信号数: {self.system_stats['total_signals']}")

            if self.system_stats['last_analysis_time']:
                last_analysis = self.system_stats['last_analysis_time']
                time_since = current_time - last_analysis
                print(f"🕐 上次分析: {int(time_since.total_seconds() / 60)}分钟前")

            # 显示系统资源使用
            try:
                import psutil
                memory_usage = psutil.virtual_memory().percent
                cpu_usage = psutil.cpu_percent(interval=1)
                print(f"💾 内存使用: {memory_usage:.1f}%")
                print(f"🖥️ CPU使用: {cpu_usage:.1f}%")
            except ImportError:
                pass

            print("-" * 40)

        except Exception as e:
            print(f"❌ 状态显示失败: {e}")

    def _update_system_stats(self):
        """更新系统统计"""
        try:
            current_time = datetime.now()
            self.system_stats['uptime'] = (current_time - self.system_start_time).total_seconds()

            # 计算成功率
            total_analyses = (self.system_stats['successful_analyses'] +
                            self.system_stats['failed_analyses'])

            if total_analyses > 0:
                success_rate = self.system_stats['successful_analyses'] / total_analyses
                self.system_stats['performance_metrics']['success_rate'] = success_rate

        except Exception:
            pass

    def _analyze_trading_rejection(self, trading_decisions: List[Dict], risk_assessment: Dict):
        """分析交易拒绝的详细原因"""
        print("\n" + "="*60)
        print("🚫 交易执行被拒绝 - 详细原因分析")
        print("="*60)

        # 1. 检查是否有交易决策
        if not trading_decisions:
            print("❌ 拒绝原因 1: 无有效交易决策")
            print("   📊 分析结果未产生任何交易信号")
            print("   💡 可能原因: 市场信号不明确、信号强度不足、信号质量过低")
        else:
            print(f"✅ 交易决策检查: 有{len(trading_decisions)}个交易决策")
            for i, decision in enumerate(trading_decisions, 1):
                print(f"   {i}. {decision.get('symbol', 'UNKNOWN')}: {decision.get('action', 'UNKNOWN')}")
                print(f"      置信度: {decision.get('confidence', 0):.1%}")
                print(f"      强度: {decision.get('strength', 0):.1%}")

        # 2. 检查风险管理决策
        risk_metrics = risk_assessment.get('risk_metrics')
        if risk_metrics:
            recommended_action = risk_metrics.recommended_action.value
            risk_level = risk_metrics.risk_level.name
            risk_score = risk_metrics.risk_score
        else:
            recommended_action = 'unknown'
            risk_level = 'UNKNOWN'
            risk_score = 0

        print(f"\n🛡️ 风险管理决策: {recommended_action}")

        if recommended_action != 'allow_trading':
            print("❌ 拒绝原因 2: 风险管理系统阻止交易")

            # 详细风险分析

            print(f"   🛡️ 风险等级: {risk_level}")
            print(f"   📈 风险评分: {risk_score:.3f}")

            # 分析具体风险因素
            risk_factors = risk_assessment.get('risk_factors', {})
            if risk_factors:
                print("   🔍 具体风险因素:")
                for factor, value in risk_factors.items():
                    if isinstance(value, (int, float)):
                        print(f"      • {factor}: {value:.3f}")
                    else:
                        print(f"      • {factor}: {value}")

            # 给出改进建议
            print("\n💡 改进建议:")
            if risk_score > 0.5:
                print("   • 风险评分过高，建议等待市场稳定")
            elif risk_level in ['HIGH', 'VERY_HIGH']:
                print("   • 风险等级过高，建议降低仓位或暂停交易")
            elif recommended_action == 'reduce_position':
                print("   • 建议减少仓位大小")
            elif recommended_action == 'wait':
                print("   • 建议等待更好的市场时机")
            else:
                print("   • 建议检查风险管理参数设置")
        else:
            print("✅ 风险管理检查: 允许交易")

        # 3. 检查交易决策质量
        if trading_decisions:
            print(f"\n📊 交易决策质量分析:")

            total_confidence = 0
            total_strength = 0
            valid_signals = 0

            for decision in trading_decisions:
                action = decision.get('action', 'NEUTRAL')
                confidence = decision.get('confidence', 0)
                strength = decision.get('strength', 0)

                if action != 'NEUTRAL':
                    valid_signals += 1
                    total_confidence += confidence
                    total_strength += strength

                    print(f"   📈 {decision.get('symbol', 'UNKNOWN')} {action}:")
                    print(f"      置信度: {confidence:.1%} {'✅' if confidence >= 0.6 else '⚠️' if confidence >= 0.4 else '❌'}")
                    print(f"      强度: {strength:.1%} {'✅' if strength >= 0.6 else '⚠️' if strength >= 0.4 else '❌'}")

                    # 检查入场价格
                    entry_price = decision.get('entry_price', 0)
                    if entry_price <= 0:
                        print(f"      ❌ 入场价格无效: {entry_price}")
                    else:
                        print(f"      ✅ 入场价格: {entry_price}")

            if valid_signals > 0:
                avg_confidence = total_confidence / valid_signals
                avg_strength = total_strength / valid_signals

                print(f"\n📊 整体信号质量:")
                print(f"   平均置信度: {avg_confidence:.1%}")
                print(f"   平均强度: {avg_strength:.1%}")

                if avg_confidence < 0.5:
                    print("   ❌ 平均置信度过低 (<50%)")
                if avg_strength < 0.5:
                    print("   ❌ 平均强度过低 (<50%)")
            else:
                print("   ❌ 无有效交易信号 (全部为NEUTRAL)")

        # 4. 系统状态检查
        print(f"\n🔧 系统状态检查:")
        print(f"   📊 ML模型状态: {'✅ 已训练' if hasattr(self.ml_engine, 'models') else '❌ 未训练'}")
        print(f"   🔗 数据连接: {'✅ 正常' if hasattr(self, 'trade_executor') else '❌ 异常'}")
        print(f"   ⚡ 执行引擎: {'✅ 就绪' if hasattr(self.trade_executor, 'mt4_client') else '❌ 未就绪'}")

        print("="*60)
        print("💡 总结: 系统优先保护资金安全，只在高质量信号且低风险时执行交易")
        print("="*60 + "\n")

    def _perform_position_management(self):
        """执行智能持仓管理"""
        try:
            # 1. 算法分析
            print("🔍 执行算法持仓分析...")
            algo_analyses = self.position_manager.analyze_all_positions()

            if algo_analyses:
                self.position_manager.print_position_analysis(algo_analyses)

                # 执行高优先级的算法建议
                high_priority_actions = [a for a in algo_analyses if a.priority >= 4]
                if high_priority_actions:
                    print(f"⚡ 执行{len(high_priority_actions)}个高优先级持仓管理动作...")
                    results = self.position_manager.execute_position_actions(high_priority_actions)
                    print(f"📊 持仓管理结果: {results['executed']}成功, {results['failed']}失败")
                else:
                    print("✅ 无需要立即执行的高优先级动作")
            else:
                print("📊 无持仓需要管理")

            # 2. LLM战略分析（1小时频率，持仓少时忽略）
            if hasattr(self, '_position_analysis_counter'):
                self._position_analysis_counter += 1
            else:
                self._position_analysis_counter = 1

            # 获取持仓信息
            mt4_positions = self.trade_executor._get_mt4_real_positions()
            position_count = len(mt4_positions) if mt4_positions else 0
            total_profit = sum(pos.get('profit', 0) for pos in mt4_positions) if mt4_positions else 0

            # LLM分析触发条件：1小时频率 + 持仓数量筛选
            cycles_per_hour = 60  # 每分钟1次循环，60次=1小时

            should_run_llm = False

            # 条件1：持仓数量少于5个，直接忽略LLM分析
            if position_count < 5:
                if self._position_analysis_counter % (cycles_per_hour * 2) == 0:  # 2小时才分析一次
                    print(f"💤 持仓数量较少({position_count}个)，跳过LLM分析")
                should_run_llm = False

            # 条件2：持仓数量5-10个，1小时分析一次
            elif 5 <= position_count < 10:
                should_run_llm = (self._position_analysis_counter % cycles_per_hour == 0)
                if should_run_llm:
                    print(f"🤖 定时LLM分析 (1小时) - 持仓:{position_count}个, 盈亏:${total_profit:.2f}")

            # 条件3：持仓数量10个以上，30分钟分析一次
            elif position_count >= 10:
                should_run_llm = (self._position_analysis_counter % (cycles_per_hour // 2) == 0)
                if should_run_llm:
                    print(f"🤖 加强LLM分析 (30分钟) - 持仓:{position_count}个, 盈亏:${total_profit:.2f}")

            # 条件4：紧急情况，立即分析
            if (position_count >= 15 or total_profit <= -30):
                should_run_llm = True
                print(f"🚨 紧急LLM分析 - 持仓:{position_count}个, 盈亏:${total_profit:.2f}")

            if should_run_llm:
                # 通过系统协调器提交LLM分析任务
                llm_task_id = self.system_coordinator.submit_task(
                    system_name='llm_analyzer',
                    action='analyze',
                    data={'type': 'portfolio_analysis'},
                    priority=SystemPriority.LOW
                )

                if llm_task_id:
                    print("🤖 执行LLM持仓组合分析...")

                    # 获取持仓数据
                    mt4_positions = self.trade_executor._get_mt4_real_positions()
                    if mt4_positions:
                        # 获取市场数据
                        market_data = self._get_current_market_data()

                        # LLM分析
                        llm_analysis = self.llm_analyzer.analyze_portfolio(mt4_positions, market_data)
                        self.llm_analyzer.print_llm_analysis(llm_analysis)

                        # 根据LLM建议执行动作
                        self._execute_llm_recommendations(llm_analysis)
                    else:
                        print("📊 无持仓进行LLM分析")
                else:
                    print("⏸️ LLM分析任务被协调器拒绝（可能在冷却期）")

        except Exception as e:
            print(f"❌ 持仓管理失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_current_market_data(self) -> Dict:
        """获取当前市场数据"""
        try:
            # 简化的市场数据获取
            return {
                'timestamp': datetime.now().isoformat(),
                'symbols': ['EURUSD', 'GBPUSD', 'AUDUSD', 'USDCHF'],
                'market_session': 'active'  # 简化处理
            }
        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return {}

    def _execute_llm_recommendations(self, llm_analysis: Dict):
        """执行LLM建议（增强版，更积极的执行）"""
        try:
            recommendations = llm_analysis.get('recommendations', [])
            if not recommendations:
                print("🤖 LLM未提供具体建议")
                return

            print(f"🤖 执行{len(recommendations)}个LLM建议...")
            executed_count = 0

            for rec in recommendations:
                action = rec.get('action', 'hold')
                symbol = rec.get('symbol', 'ALL')
                priority = rec.get('priority', 1)
                reason = rec.get('reason', '未知原因')

                print(f"🤖 LLM建议: {action} {symbol} (优先级:{priority}) - {reason}")

                if action == 'close':
                    # 降低执行门槛，优先级>=2就执行
                    if priority >= 2:
                        # 执行平仓建议
                        mt4_positions = self.trade_executor._get_mt4_real_positions()
                        for pos in mt4_positions:
                            if symbol == 'ALL' or pos['symbol'] == symbol:
                                print(f"🤖 LLM建议平仓: {pos['symbol']} {pos['order_id']} (${pos.get('profit', 0):.2f})")
                                success = self.trade_executor.close_position(pos['order_id'])
                                if success:
                                    print(f"✅ LLM建议平仓成功: {pos['order_id']}")
                                    executed_count += 1
                                else:
                                    print(f"❌ LLM建议平仓失败: {pos['order_id']}")
                    else:
                        print(f"⏸️ 优先级{priority}过低，跳过执行")

                elif action == 'modify':
                    print(f"💡 LLM建议修改订单，但功能待实现")

                else:
                    print(f"📝 LLM建议: {action} - {reason}")

            print(f"📊 LLM建议执行结果: {executed_count}个动作成功执行")

            # 如果持仓数量仍然过多，给出警告
            mt4_positions = self.trade_executor._get_mt4_real_positions()
            remaining_positions = len(mt4_positions) if mt4_positions else 0
            if remaining_positions >= 15:
                print(f"⚠️ 警告: 执行LLM建议后仍有{remaining_positions}个持仓，建议继续清理")

        except Exception as e:
            print(f"❌ 执行LLM建议失败: {e}")
            import traceback
            traceback.print_exc()

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n⚠️ 收到信号 {signum}，准备关闭系统...")
        self.running = False

    def _get_current_positions_for_strategy(self) -> Dict:
        """获取当前持仓信息供策略使用"""
        try:
            positions = {}
            active_orders = self.trade_executor.get_active_positions()

            for order in active_orders:
                symbol = order.symbol
                if symbol not in positions:
                    positions[symbol] = {
                        'orders': [],
                        'total_size': 0.0,
                        'total_value': 0.0,
                        'avg_entry_price': 0.0
                    }

                positions[symbol]['orders'].append({
                    'order_id': order.order_id,
                    'action': order.order_type.value,
                    'size': order.volume,
                    'entry_price': order.entry_price,
                    'current_profit': getattr(order, 'current_profit', 0.0)
                })

                positions[symbol]['total_size'] += order.volume
                positions[symbol]['total_value'] += order.volume * order.entry_price

            # 计算平均入场价格
            for symbol, pos_info in positions.items():
                if pos_info['total_size'] > 0:
                    pos_info['avg_entry_price'] = pos_info['total_value'] / pos_info['total_size']
                    pos_info['weight'] = pos_info['total_size'] / sum(p['total_size'] for p in positions.values())

            return positions

        except Exception as e:
            print(f"❌ 获取持仓信息失败: {e}")
            return {}

    def _convert_master_decisions_to_trades(self, master_decisions: List) -> List:
        """将主策略决策转换为系统可执行的交易决策"""
        try:
            from strategies.professional_portfolio_strategy import PortfolioAction

            trading_decisions = []

            for decision in master_decisions:
                if decision.action in [PortfolioAction.ENTER_LONG, PortfolioAction.ENTER_SHORT]:
                    # 转换为系统交易决策格式
                    trade_decision = {
                        'symbol': decision.symbol,
                        'action': 'BUY' if decision.action == PortfolioAction.ENTER_LONG else 'SELL',
                        'volume': decision.size,
                        'entry_price': decision.entry_price,
                        'stop_loss': decision.stop_loss,
                        'take_profit': decision.take_profit,
                        'confidence': decision.confidence,
                        'reasoning': decision.reasoning,
                        'risk_reward_ratio': decision.risk_reward_ratio
                    }
                    trading_decisions.append(trade_decision)
                elif decision.action == PortfolioAction.EXIT_POSITION:
                    # 平仓决策
                    trade_decision = {
                        'symbol': decision.symbol,
                        'action': 'CLOSE',
                        'volume': decision.size,
                        'reasoning': decision.reasoning
                    }
                    trading_decisions.append(trade_decision)
                elif decision.action == PortfolioAction.REBALANCE:
                    # 重新平衡决策 - 这是关键修复！
                    if decision.size < 0:
                        # 负数表示减仓（部分平仓）
                        trade_decision = {
                            'symbol': decision.symbol,
                            'action': 'CLOSE_PARTIAL',
                            'volume': abs(decision.size),  # 转为正数
                            'reasoning': decision.reasoning,
                            'confidence': decision.confidence
                        }
                        trading_decisions.append(trade_decision)
                        print(f"🔄 转换重新平衡决策: {decision.symbol} 减仓 {abs(decision.size):.3f}手")
                    elif decision.size > 0:
                        # 正数表示加仓
                        trade_decision = {
                            'symbol': decision.symbol,
                            'action': 'BUY',  # 默认买入，实际应根据现有持仓方向
                            'volume': decision.size,
                            'reasoning': decision.reasoning,
                            'confidence': decision.confidence
                        }
                        trading_decisions.append(trade_decision)
                        print(f"🔄 转换重新平衡决策: {decision.symbol} 加仓 {decision.size:.3f}手")

            return trading_decisions

        except Exception as e:
            print(f"❌ 转换主策略决策失败: {e}")
            return []

    def _create_risk_assessment_from_master_decision(self, master_decision) -> Dict:
        """从主策略决策创建风险评估（保持兼容性）"""
        try:
            # 创建兼容的风险评估结构
            from core.risk_engine.advanced_risk_manager import RiskAction, RiskLevel

            # 根据置信度和市场条件确定风险等级
            if master_decision.confidence > 0.7:
                recommended_action = RiskAction.ALLOW_TRADING
                risk_level = RiskLevel.LOW
            elif master_decision.confidence > 0.5:
                recommended_action = RiskAction.REDUCE_POSITION
                risk_level = RiskLevel.MEDIUM
            else:
                recommended_action = RiskAction.STOP_NEW_TRADES
                risk_level = RiskLevel.HIGH

            risk_metrics = type('RiskMetrics', (), {
                'recommended_action': recommended_action,
                'risk_level': risk_level,
                'risk_score': 1.0 - master_decision.confidence,
                'confidence': master_decision.confidence
            })()

            return {
                'risk_metrics': risk_metrics,
                'overall_risk': 'LOW' if master_decision.confidence > 0.7 else 'MEDIUM' if master_decision.confidence > 0.5 else 'HIGH',
                'market_condition': master_decision.market_condition.value,
                'strategy_used': master_decision.strategy_used.value
            }

        except Exception as e:
            print(f"❌ 创建风险评估失败: {e}")
            return {'risk_metrics': None}

    def _display_master_decision_results(self, master_decision, trading_decisions: List):
        """显示主策略决策结果"""
        try:
            print("\n" + "="*60)
            print("📊 主策略决策结果")
            print("="*60)

            print(f"🧠 使用策略: {master_decision.strategy_used.value}")
            print(f"🌍 市场条件: {master_decision.market_condition.value}")
            print(f"💪 整体置信度: {master_decision.confidence:.2%}")
            print(f"📝 决策说明: {master_decision.reasoning}")

            if master_decision.decisions:
                print(f"\n📋 策略决策详情 ({len(master_decision.decisions)}个):")
                for i, decision in enumerate(master_decision.decisions, 1):
                    print(f"  {i}. {decision.symbol} - {decision.action.value}")
                    print(f"     仓位: {decision.size:.3f}手")
                    print(f"     置信度: {decision.confidence:.2%}")
                    print(f"     说明: {decision.reasoning}")
                    if hasattr(decision, 'risk_reward_ratio') and decision.risk_reward_ratio > 0:
                        print(f"     风险回报比: 1:{decision.risk_reward_ratio:.2f}")
                    print()

            if trading_decisions:
                print(f"⚡ 可执行交易决策 ({len(trading_decisions)}个):")
                for i, trade in enumerate(trading_decisions, 1):
                    print(f"  {i}. {trade['symbol']} {trade['action']} {trade.get('volume', 0):.3f}手")
                    if 'reasoning' in trade:
                        print(f"     原因: {trade['reasoning']}")
            else:
                print("⏸️ 无可执行的交易决策")

            print("="*60)

        except Exception as e:
            print(f"❌ 显示结果失败: {e}")

    def _on_parameters_updated(self, optimization_results: List):
        """参数更新回调"""
        try:
            print("\n🔧 学习系统参数优化完成:")
            for result in optimization_results:
                print(f"   📊 {result.parameter_name}: {result.old_value} → {result.new_value}")
                print(f"      改进评分: {result.improvement_score:.3f}")
                print(f"      置信度: {result.confidence:.1%}")
                print(f"      原因: {result.reason}")

            # 更新策略引擎参数
            self._update_strategy_parameters()

        except Exception as e:
            self.logger.error(f"参数更新回调失败: {e}")

    def _update_strategy_parameters(self):
        """更新策略参数"""
        try:
            # 获取最新的优化参数
            current_params = self.learning_coordinator.get_current_parameters()

            # 更新主策略引擎的参数
            if hasattr(self.master_strategy, 'portfolio_strategy'):
                strategy = self.master_strategy.portfolio_strategy

                # 更新策略参数
                if 'max_portfolio_risk' in current_params:
                    strategy.max_portfolio_risk = current_params['max_portfolio_risk']
                if 'max_correlation_exposure' in current_params:
                    strategy.max_correlation_exposure = current_params['max_correlation_exposure']
                if 'min_confidence_threshold' in current_params:
                    strategy.min_confidence_threshold = current_params['min_confidence_threshold']
                if 'rebalance_threshold' in current_params:
                    strategy.rebalance_threshold = current_params['rebalance_threshold']

            print("✅ 策略参数更新完成")

        except Exception as e:
            self.logger.error(f"更新策略参数失败: {e}")

    def _record_trade_execution(self, trading_decisions: List):
        """记录交易执行"""
        try:
            for decision in trading_decisions:
                if decision.get('action') in ['BUY', 'SELL']:
                    # 准备交易数据
                    trade_data = {
                        'symbol': decision['symbol'],
                        'action': decision['action'],
                        'entry_price': decision.get('entry_price', 0.0),
                        'volume': decision.get('volume', 0.01),
                        'stop_loss': decision.get('stop_loss', 0.0),
                        'take_profit': decision.get('take_profit', 0.0),
                        'confidence': decision.get('confidence', 0.5),
                        'strategy_used': 'portfolio_strategy',
                        'market_condition': 'unknown',
                        'rsi': 50.0,  # 这些应该从实际分析中获取
                        'ma_20': decision.get('entry_price', 0.0),
                        'ma_50': decision.get('entry_price', 0.0),
                        'atr': 0.002,
                        'volatility': 0.01
                    }

                    # 记录交易开仓
                    trade_id = self.learning_coordinator.record_trade_entry(trade_data)

                    if trade_id:
                        print(f"📝 记录交易开仓: {trade_id}")

                        # 这里应该保存trade_id以便后续记录平仓
                        # 在实际实现中，需要与交易执行引擎集成

        except Exception as e:
            self.logger.error(f"记录交易执行失败: {e}")

    def get_learning_statistics(self) -> Dict:
        """获取学习统计"""
        try:
            return self.learning_coordinator.get_learning_statistics()
        except Exception as e:
            self.logger.error(f"获取学习统计失败: {e}")
            return {'error': str(e)}

    def _shutdown(self):
        """系统关闭"""
        try:
            print("\n🔄 正在关闭 QuantumForex Pro...")

            self.running = False

            # 停止内存监控
            print("🔍 停止内存监控...")
            memory_optimizer.stop_monitoring()

            # 停止MT4监控
            if hasattr(self, 'mt4_monitor'):
                print("🔗 停止MT4交易监控...")
                self.mt4_monitor.stop_monitoring()

            # 停止学习系统
            if hasattr(self, 'learning_coordinator'):
                print("📚 停止学习系统...")
                self.learning_coordinator.stop()

            # 显示交易摘要（包含盈亏分析）
            if hasattr(self, 'trade_executor') and self.trade_executor:
                self.trade_executor.print_trading_summary()

            # 显示运行统计
            uptime = datetime.now() - self.system_start_time
            print(f"📊 系统运行统计:")
            print(f"   ⏱️ 总运行时间: {str(uptime).split('.')[0]}")
            print(f"   ✅ 成功分析: {self.system_stats['successful_analyses']}")
            print(f"   ❌ 失败分析: {self.system_stats['failed_analyses']}")
            print(f"   📊 总信号数: {self.system_stats['total_signals']}")

            if 'success_rate' in self.system_stats['performance_metrics']:
                success_rate = self.system_stats['performance_metrics']['success_rate']
                print(f"   📈 分析成功率: {success_rate:.1%}")

            # 最后的内存清理
            print("🧹 执行最终内存清理...")
            emergency_memory_cleanup()

            print("✅ QuantumForex Pro 已安全关闭")
            print("感谢使用世界顶级量化交易系统！")

        except Exception as e:
            print(f"❌ 关闭过程出错: {e}")

def main():
    """主函数"""
    try:
        print("🌟 欢迎使用 QuantumForex Pro")
        print("🏆 世界顶级量化交易系统")
        print("💎 专为稳定收益、高胜率、低回撤而设计")
        print()

        # 创建并启动系统
        quantum_forex = QuantumForexPro()
        quantum_forex.start()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        print(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
