#!/usr/bin/env python3
"""
真实回测引擎
使用真实历史数据验证模型效果
"""

import numpy as np
import pandas as pd
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from pathlib import Path

@dataclass
class BacktestMetrics:
    """回测指标"""
    # 基础指标
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float

    # 收益指标
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    calmar_ratio: float

    # 交易质量
    avg_win: float
    avg_loss: float
    profit_factor: float
    max_consecutive_wins: int
    max_consecutive_losses: int

    # 预测准确性
    prediction_accuracy: float
    signal_quality: float

    # 风险指标
    volatility: float
    var_95: float

    def __str__(self):
        return f"""
📊 回测结果报告
{'='*50}
📈 交易统计:
   总交易数: {self.total_trades}
   盈利交易: {self.winning_trades}
   亏损交易: {self.losing_trades}
   胜率: {self.win_rate:.1%}

💰 收益指标:
   总收益率: {self.total_return:.1%}
   年化收益率: {self.annual_return:.1%}
   最大回撤: {self.max_drawdown:.1%}
   夏普比率: {self.sharpe_ratio:.2f}
   卡尔玛比率: {self.calmar_ratio:.2f}

🎯 预测质量:
   预测准确率: {self.prediction_accuracy:.1%}
   信号质量: {self.signal_quality:.1%}

🛡️ 风险指标:
   收益波动率: {self.volatility:.1%}
   95% VaR: {self.var_95:.1%}
   盈亏比: {self.profit_factor:.2f}
"""

class RealBacktestEngine:
    """真实回测引擎"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 回测参数
        self.initial_capital = 10000.0
        self.transaction_cost = 0.0001  # 0.01%
        self.slippage = 0.0001  # 0.01%

        # 交易参数
        self.risk_per_trade = 0.02  # 每笔交易风险2%
        self.stop_loss_pips = 20
        self.take_profit_pips = 40
        self.min_confidence = 0.6

    def run_comprehensive_backtest(self,
                                 model_path: str,
                                 data_days: int = 30) -> BacktestMetrics:
        """运行综合回测"""
        try:
            self.logger.info(f"🚀 开始综合回测: {model_path}")

            # 1. 加载模型
            model, scaler = self._load_model(model_path)
            if model is None:
                raise ValueError("模型加载失败")

            # 2. 获取历史数据
            historical_data = self._get_historical_data(data_days)
            if historical_data.empty:
                raise ValueError("历史数据获取失败")

            # 3. 生成预测
            predictions = self._generate_model_predictions(model, scaler, historical_data)
            if not predictions:
                raise ValueError("预测生成失败")

            # 4. 验证预测准确性
            prediction_accuracy = self._validate_prediction_accuracy(predictions, historical_data)

            # 5. 模拟交易
            trades = self._simulate_trading(predictions, historical_data)

            # 6. 计算性能指标
            metrics = self._calculate_comprehensive_metrics(
                trades, historical_data, prediction_accuracy
            )

            self.logger.info(f"✅ 回测完成: {metrics.total_trades}笔交易")
            return metrics

        except Exception as e:
            self.logger.error(f"❌ 回测失败: {e}")
            raise

    def _load_model(self, model_path: str) -> Tuple[Any, Any]:
        """加载模型和缩放器"""
        try:
            model_file = Path(model_path)
            if not model_file.exists():
                self.logger.error(f"模型文件不存在: {model_path}")
                return None, None

            # 加载模型
            model = joblib.load(model_file)

            # 查找对应的scaler文件
            scaler_path = model_file.parent / f"{model_file.stem}_scaler.pkl"
            if scaler_path.exists():
                scaler = joblib.load(scaler_path)
            else:
                # 如果没有scaler，创建一个标准scaler
                from sklearn.preprocessing import StandardScaler
                scaler = StandardScaler()
                self.logger.warning(f"未找到scaler文件，使用默认StandardScaler")

            self.logger.info(f"✅ 模型加载成功: {type(model).__name__}")
            return model, scaler

        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {e}")
            return None, None

    def _get_historical_data(self, days: int) -> pd.DataFrame:
        """获取历史数据"""
        try:
            # 尝试从数据库获取真实历史数据
            try:
                from data_collector.forex_data_collector import ForexDataCollector
                collector = ForexDataCollector()

                end_time = datetime.now()
                start_time = end_time - timedelta(days=days)

                data = collector.get_historical_data(
                    symbol='EURUSD',
                    timeframe='5min',
                    start_time=start_time,
                    end_time=end_time
                )

                if data is not None and len(data) > 100:
                    self.logger.info(f"✅ 获取真实历史数据: {len(data)}条")
                    return data
                else:
                    self.logger.warning("真实数据不足，使用模拟数据")

            except Exception as e:
                self.logger.warning(f"获取真实数据失败: {e}，使用模拟数据")

            # 生成高质量的模拟历史数据
            return self._generate_realistic_data(days)

        except Exception as e:
            self.logger.error(f"❌ 历史数据获取失败: {e}")
            return pd.DataFrame()

    def _generate_realistic_data(self, days: int) -> pd.DataFrame:
        """生成逼真的历史数据"""
        try:
            # 生成更多数据点
            periods = days * 24 * 12  # 5分钟数据

            # 基于真实市场特征生成数据
            np.random.seed(42)

            # 价格基础参数
            base_price = 1.0800
            daily_volatility = 0.008  # 日波动率0.8%
            trend_strength = 0.0002   # 趋势强度

            # 生成价格序列
            prices = [base_price]
            volumes = []

            for i in range(periods):
                # 时间因子（模拟市场开闭盘）
                hour = (i // 12) % 24
                time_factor = 1.0
                if 6 <= hour <= 16:  # 欧美交易时段
                    time_factor = 1.5
                elif 20 <= hour <= 24 or 0 <= hour <= 2:  # 美洲时段
                    time_factor = 1.2

                # 价格变化
                random_change = np.random.normal(0, daily_volatility / np.sqrt(288)) * time_factor
                trend_change = trend_strength * np.sin(i / 100) / 288  # 周期性趋势

                new_price = prices[-1] * (1 + random_change + trend_change)
                prices.append(max(0.5, new_price))

                # 成交量（与波动率相关）
                base_volume = 2000
                volume_factor = abs(random_change) * 10000 + 1
                volume = int(base_volume * volume_factor * time_factor)
                volumes.append(volume)

            # 生成OHLC数据
            data = []
            for i in range(1, len(prices)):
                open_price = prices[i-1]
                close_price = prices[i]

                # 生成高低价
                price_range = abs(close_price - open_price) * 2
                high_price = max(open_price, close_price) + np.random.uniform(0, price_range)
                low_price = min(open_price, close_price) - np.random.uniform(0, price_range)

                data.append({
                    'timestamp': datetime.now() - timedelta(minutes=5*(periods-i)),
                    'symbol': 'EURUSD',
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'volume': volumes[i-1]
                })

            df = pd.DataFrame(data)
            df = df.sort_values('timestamp').reset_index(drop=True)

            self.logger.info(f"✅ 生成逼真历史数据: {len(df)}条")
            return df

        except Exception as e:
            self.logger.error(f"❌ 生成历史数据失败: {e}")
            return pd.DataFrame()

    def _generate_model_predictions(self, model, scaler, data: pd.DataFrame) -> Dict[int, Dict]:
        """生成模型预测"""
        try:
            predictions = {}

            # 计算技术指标特征
            data_with_features = self._calculate_features(data)

            for idx, row in data_with_features.iterrows():
                try:
                    # 确保有足够的历史数据
                    if idx < 20:  # 需要至少20个数据点计算指标
                        continue

                    # 提取特征
                    features = self._extract_features(data_with_features, idx)
                    if features is None:
                        continue

                    # 标准化特征
                    if hasattr(scaler, 'transform'):
                        try:
                            features_scaled = scaler.transform(features.reshape(1, -1))
                        except:
                            features_scaled = features.reshape(1, -1)
                    else:
                        features_scaled = features.reshape(1, -1)

                    # 模型预测
                    if hasattr(model, 'predict_proba'):
                        # 分类模型
                        proba = model.predict_proba(features_scaled)[0]
                        prediction = model.predict(features_scaled)[0]
                        confidence = max(proba)

                        # 转换为交易信号
                        if len(proba) > 1:
                            signal = proba[1] - proba[0]  # 多头概率 - 空头概率
                        else:
                            signal = prediction
                    else:
                        # 回归模型
                        prediction = model.predict(features_scaled)[0]
                        signal = prediction
                        confidence = min(abs(prediction) * 2, 1.0)  # 简化置信度计算

                    predictions[idx] = {
                        'signal': signal,
                        'confidence': confidence,
                        'prediction': prediction,
                        'timestamp': row['timestamp'],
                        'price': row['close']
                    }

                except Exception as e:
                    continue

            self.logger.info(f"✅ 生成预测: {len(predictions)}个")
            return predictions

        except Exception as e:
            self.logger.error(f"❌ 预测生成失败: {e}")
            return {}

    def _calculate_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标特征"""
        try:
            df = data.copy()

            # 价格特征
            df['price_change'] = df['close'].pct_change()
            df['price_volatility'] = df['price_change'].rolling(20).std()
            df['high_low_ratio'] = (df['high'] - df['low']) / df['close']

            # 移动平均
            for period in [5, 10, 20]:
                df[f'ma_{period}'] = df['close'].rolling(period).mean()
                df[f'price_ma_ratio_{period}'] = df['close'] / df[f'ma_{period}']

            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))

            # 布林带
            df['bb_middle'] = df['close'].rolling(20).mean()
            df['bb_std'] = df['close'].rolling(20).std()
            df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * 2)
            df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * 2)
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])

            # 成交量指标
            df['volume_ma'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']

            return df

        except Exception as e:
            self.logger.error(f"❌ 特征计算失败: {e}")
            return data

    def _extract_features(self, data: pd.DataFrame, idx: int) -> np.ndarray:
        """提取单行特征"""
        try:
            row = data.iloc[idx]

            # 基础特征
            features = [
                row['close'],
                row['price_change'],
                row['price_volatility'],
                row['high_low_ratio'],
                row['rsi'],
                row['bb_position'],
                row['volume_ratio']
            ]

            # 移动平均比率
            for period in [5, 10, 20]:
                features.append(row[f'price_ma_ratio_{period}'])

            # 滞后特征
            for lag in [1, 2, 3]:
                if idx >= lag:
                    features.append(data.iloc[idx-lag]['price_change'])
                else:
                    features.append(0)

            # 过滤NaN值
            features = np.array(features, dtype=float)
            features = np.nan_to_num(features, nan=0.0)

            return features

        except Exception as e:
            self.logger.error(f"❌ 特征提取失败: {e}")
            return None

    def _validate_prediction_accuracy(self, predictions: Dict, data: pd.DataFrame) -> float:
        """验证预测准确性"""
        try:
            correct_predictions = 0
            total_predictions = 0

            for idx, pred in predictions.items():
                if idx + 1 >= len(data):
                    continue

                # 获取下一期的实际价格变化
                current_price = data.iloc[idx]['close']
                next_price = data.iloc[idx + 1]['close']
                actual_change = (next_price - current_price) / current_price

                # 预测方向
                predicted_direction = 1 if pred['signal'] > 0 else -1
                actual_direction = 1 if actual_change > 0 else -1

                # 只考虑高置信度的预测
                if pred['confidence'] > self.min_confidence:
                    total_predictions += 1
                    if predicted_direction == actual_direction:
                        correct_predictions += 1

            accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
            self.logger.info(f"📊 预测准确率: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
            return accuracy

        except Exception as e:
            self.logger.error(f"❌ 预测验证失败: {e}")
            return 0.0

    def _simulate_trading(self, predictions: Dict, data: pd.DataFrame) -> List[Dict]:
        """模拟交易"""
        try:
            trades = []
            open_trades = []
            current_capital = self.initial_capital

            for idx, row in data.iterrows():
                current_price = row['close']
                current_time = row['timestamp']

                # 检查平仓条件
                for trade in open_trades.copy():
                    should_close, reason = self._should_close_trade(trade, current_price)
                    if should_close:
                        closed_trade = self._close_trade(trade, current_price, current_time, reason)
                        trades.append(closed_trade)
                        open_trades.remove(trade)
                        current_capital += closed_trade['profit_usd']

                # 检查开仓信号
                if idx in predictions:
                    pred = predictions[idx]

                    # 信号过滤
                    if (pred['confidence'] > self.min_confidence and
                        abs(pred['signal']) > 0.3 and
                        len(open_trades) < 3):  # 限制同时开仓数

                        trade = self._open_trade(pred, current_price, current_time)
                        if trade:
                            open_trades.append(trade)

            # 关闭剩余交易
            if open_trades:
                final_price = data.iloc[-1]['close']
                final_time = data.iloc[-1]['timestamp']
                for trade in open_trades:
                    closed_trade = self._close_trade(trade, final_price, final_time, "回测结束")
                    trades.append(closed_trade)

            self.logger.info(f"✅ 交易模拟完成: {len(trades)}笔交易")
            return trades

        except Exception as e:
            self.logger.error(f"❌ 交易模拟失败: {e}")
            return []

    def _should_close_trade(self, trade: Dict, current_price: float) -> Tuple[bool, str]:
        """判断是否应该平仓"""
        try:
            entry_price = trade['entry_price']
            direction = trade['direction']

            if direction == 'LONG':
                pips_profit = (current_price - entry_price) * 10000
            else:
                pips_profit = (entry_price - current_price) * 10000

            # 止损
            if pips_profit <= -self.stop_loss_pips:
                return True, "止损"

            # 止盈
            if pips_profit >= self.take_profit_pips:
                return True, "止盈"

            # 时间止损（持仓超过4小时）
            holding_time = datetime.now() - trade['entry_time']
            if holding_time.total_seconds() > 4 * 3600:
                return True, "时间止损"

            return False, ""

        except Exception as e:
            return False, "错误"

    def _open_trade(self, prediction: Dict, price: float, time: datetime) -> Dict:
        """开仓"""
        try:
            direction = 'LONG' if prediction['signal'] > 0 else 'SHORT'

            # 计算仓位大小
            risk_amount = self.initial_capital * self.risk_per_trade
            pip_value = 1.0  # 简化
            position_size = risk_amount / (self.stop_loss_pips * pip_value)
            position_size = max(0.01, min(position_size, 1.0))  # 限制仓位

            trade = {
                'entry_time': time,
                'entry_price': price,
                'direction': direction,
                'size': position_size,
                'confidence': prediction['confidence'],
                'signal_strength': abs(prediction['signal'])
            }

            return trade

        except Exception as e:
            self.logger.error(f"❌ 开仓失败: {e}")
            return None

    def _close_trade(self, trade: Dict, price: float, time: datetime, reason: str) -> Dict:
        """平仓"""
        try:
            entry_price = trade['entry_price']
            direction = trade['direction']
            size = trade['size']

            # 计算盈亏
            if direction == 'LONG':
                pips_profit = (price - entry_price) * 10000
            else:
                pips_profit = (entry_price - price) * 10000

            # 转换为美元盈亏
            pip_value = 1.0 * size  # 简化计算
            gross_profit = pips_profit * pip_value

            # 扣除交易成本
            transaction_cost = size * 100000 * self.transaction_cost * 2  # 开仓+平仓
            net_profit = gross_profit - transaction_cost

            closed_trade = trade.copy()
            closed_trade.update({
                'exit_time': time,
                'exit_price': price,
                'pips_profit': pips_profit,
                'profit_usd': net_profit,
                'close_reason': reason,
                'holding_minutes': (time - trade['entry_time']).total_seconds() / 60
            })

            return closed_trade

        except Exception as e:
            self.logger.error(f"❌ 平仓失败: {e}")
            return trade

    def _calculate_comprehensive_metrics(self, trades: List[Dict],
                                       data: pd.DataFrame,
                                       prediction_accuracy: float) -> BacktestMetrics:
        """计算综合指标"""
        try:
            if not trades:
                return self._create_empty_metrics()

            # 基础统计
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t['profit_usd'] > 0])
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades

            # 盈亏统计
            profits = [t['profit_usd'] for t in trades if t['profit_usd'] > 0]
            losses = [t['profit_usd'] for t in trades if t['profit_usd'] < 0]

            total_profit = sum(profits) if profits else 0
            total_loss = abs(sum(losses)) if losses else 0
            net_profit = total_profit - total_loss

            avg_win = np.mean(profits) if profits else 0
            avg_loss = abs(np.mean(losses)) if losses else 0
            profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')

            # 收益率
            total_return = net_profit / self.initial_capital

            # 年化收益率（假设回测期间）
            days = (data['timestamp'].max() - data['timestamp'].min()).days
            annual_return = total_return * (365 / max(days, 1))

            # 计算回撤
            equity_curve = self._calculate_equity_curve(trades)
            max_drawdown = self._calculate_max_drawdown(equity_curve)

            # 夏普比率
            returns = [t['profit_usd'] / self.initial_capital for t in trades]
            if returns:
                sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            else:
                sharpe_ratio = 0

            # 卡尔玛比率
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0

            # 连续盈亏
            consecutive_wins, consecutive_losses = self._calculate_consecutive_trades(trades)

            # 信号质量
            high_confidence_trades = len([t for t in trades if t['confidence'] > 0.7])
            signal_quality = high_confidence_trades / total_trades if total_trades > 0 else 0

            # 风险指标
            volatility = np.std(returns) if returns else 0
            var_95 = np.percentile([t['profit_usd'] for t in trades], 5) if trades else 0

            return BacktestMetrics(
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_return=total_return,
                annual_return=annual_return,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                calmar_ratio=calmar_ratio,
                avg_win=avg_win,
                avg_loss=avg_loss,
                profit_factor=profit_factor,
                max_consecutive_wins=consecutive_wins,
                max_consecutive_losses=consecutive_losses,
                prediction_accuracy=prediction_accuracy,
                signal_quality=signal_quality,
                volatility=volatility,
                var_95=var_95
            )

        except Exception as e:
            self.logger.error(f"❌ 指标计算失败: {e}")
            return self._create_empty_metrics()

    def _calculate_equity_curve(self, trades: List[Dict]) -> List[float]:
        """计算资金曲线"""
        equity = [self.initial_capital]
        for trade in trades:
            equity.append(equity[-1] + trade['profit_usd'])
        return equity

    def _calculate_max_drawdown(self, equity_curve: List[float]) -> float:
        """计算最大回撤"""
        if len(equity_curve) < 2:
            return 0.0

        peak = equity_curve[0]
        max_dd = 0.0

        for equity in equity_curve[1:]:
            if equity > peak:
                peak = equity
            else:
                drawdown = (peak - equity) / peak
                max_dd = max(max_dd, drawdown)

        return max_dd

    def _calculate_consecutive_trades(self, trades: List[Dict]) -> Tuple[int, int]:
        """计算最大连续盈亏"""
        if not trades:
            return 0, 0

        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0

        for trade in trades:
            if trade['profit_usd'] > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)

        return max_wins, max_losses

    def _create_empty_metrics(self) -> BacktestMetrics:
        """创建空指标"""
        return BacktestMetrics(
            total_trades=0, winning_trades=0, losing_trades=0, win_rate=0,
            total_return=0, annual_return=0, max_drawdown=0, sharpe_ratio=0,
            calmar_ratio=0, avg_win=0, avg_loss=0, profit_factor=0,
            max_consecutive_wins=0, max_consecutive_losses=0,
            prediction_accuracy=0, signal_quality=0, volatility=0, var_95=0
        )
