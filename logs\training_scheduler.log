2025-05-30 02:28:20,493 - __main__ - INFO - QuantumForex MLTrainer 训练调度器
2025-05-30 02:28:20,498 - __main__ - INFO - ============================================================
2025-05-30 02:28:21,222 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 02:28:21,223 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 02:28:21,223 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 02:28:21,223 - __main__ - INFO - 调度器功能:
2025-05-30 02:28:21,223 - __main__ - INFO -    - 定时触发: 根据配置的频率自动训练
2025-05-30 02:28:21,223 - __main__ - INFO -    - 数据驱动: 检测到足够新数据时自动训练
2025-05-30 02:28:21,225 - __main__ - INFO -    - 性能驱动: 模型性能下降时自动重训练
2025-05-30 02:28:21,225 - __main__ - INFO -    - 手动触发: 支持手动强制触发训练
2025-05-30 02:28:21,225 - __main__ - INFO - 
2025-05-30 02:28:21,225 - __main__ - INFO - 控制命令:
2025-05-30 02:28:21,225 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 02:28:21,225 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 02:28:21,225 - __main__ - INFO - 
2025-05-30 02:28:21,226 - __main__ - INFO - 调度器状态:
2025-05-30 02:28:21,226 - __main__ - INFO -    运行状态: 运行中
2025-05-30 02:28:21,226 - __main__ - INFO -    触发次数: 0
2025-05-30 02:28:21,226 - __main__ - INFO - 
2025-05-30 02:28:21,226 - __main__ - INFO - 调度器正在运行，等待触发条件...
2025-05-30 02:41:54,697 - __main__ - INFO - 🚀 QuantumForex MLTrainer 训练调度器
2025-05-30 02:41:54,698 - __main__ - INFO - ============================================================
2025-05-30 02:41:55,253 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 02:41:55,254 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 02:41:55,254 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 02:41:55,254 - __main__ - INFO - 📅 调度器功能:
2025-05-30 02:41:55,255 - __main__ - INFO -    ✅ 定时触发 - 根据配置的频率自动训练
2025-05-30 02:41:55,255 - __main__ - INFO -    ✅ 数据驱动 - 检测到足够新数据时自动训练
2025-05-30 02:41:55,255 - __main__ - INFO -    ✅ 性能驱动 - 模型性能下降时自动重训练
2025-05-30 02:41:55,255 - __main__ - INFO -    ✅ 手动触发 - 支持手动强制触发训练
2025-05-30 02:41:55,255 - __main__ - INFO - 
2025-05-30 02:41:55,255 - __main__ - INFO - 🔧 控制命令:
2025-05-30 02:41:55,255 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 02:41:55,255 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 02:41:55,255 - __main__ - INFO - 
2025-05-30 02:41:55,255 - __main__ - INFO - 📊 调度器状态:
2025-05-30 02:41:55,256 - __main__ - INFO -    运行状态: 运行中
2025-05-30 02:41:55,256 - __main__ - INFO -    触发次数: 0
2025-05-30 02:41:55,256 - __main__ - INFO - 
2025-05-30 02:41:55,256 - __main__ - INFO - 🔄 调度器正在运行，等待触发条件...
2025-05-30 02:50:18,229 - __main__ - INFO - 🚀 QuantumForex MLTrainer 训练调度器
2025-05-30 02:50:18,229 - __main__ - INFO - ============================================================
2025-05-30 02:50:18,782 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 02:50:18,782 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 02:50:18,783 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 02:50:18,783 - __main__ - INFO - 📅 调度器功能:
2025-05-30 02:50:18,783 - __main__ - INFO -    ✅ 定时触发 - 根据配置的频率自动训练
2025-05-30 02:50:18,784 - __main__ - INFO -    ✅ 数据驱动 - 检测到足够新数据时自动训练
2025-05-30 02:50:18,784 - __main__ - INFO -    ✅ 性能驱动 - 模型性能下降时自动重训练
2025-05-30 02:50:18,784 - __main__ - INFO -    ✅ 手动触发 - 支持手动强制触发训练
2025-05-30 02:50:18,784 - __main__ - INFO - 
2025-05-30 02:50:18,785 - __main__ - INFO - 🔧 控制命令:
2025-05-30 02:50:18,785 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 02:50:18,785 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 02:50:18,785 - __main__ - INFO - 
2025-05-30 02:50:18,786 - __main__ - INFO - 📊 调度器状态:
2025-05-30 02:50:18,786 - __main__ - INFO -    运行状态: 运行中
2025-05-30 02:50:18,786 - __main__ - INFO -    触发次数: 0
2025-05-30 02:50:18,786 - __main__ - INFO - 
2025-05-30 02:50:18,787 - __main__ - INFO - 🔄 调度器正在运行，等待触发条件...
2025-05-30 02:56:59,114 - __main__ - INFO - 🚀 QuantumForex MLTrainer 训练调度器
2025-05-30 02:56:59,114 - __main__ - INFO - ============================================================
2025-05-30 02:56:59,720 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 02:56:59,721 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 02:56:59,721 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 02:56:59,722 - __main__ - INFO - 📅 调度器功能:
2025-05-30 02:56:59,722 - __main__ - INFO -    ✅ 定时触发 - 根据配置的频率自动训练
2025-05-30 02:56:59,722 - __main__ - INFO -    ✅ 数据驱动 - 检测到足够新数据时自动训练
2025-05-30 02:56:59,722 - __main__ - INFO -    ✅ 性能驱动 - 模型性能下降时自动重训练
2025-05-30 02:56:59,722 - __main__ - INFO -    ✅ 手动触发 - 支持手动强制触发训练
2025-05-30 02:56:59,722 - __main__ - INFO - 
2025-05-30 02:56:59,723 - __main__ - INFO - 🔧 控制命令:
2025-05-30 02:56:59,723 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 02:56:59,723 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 02:56:59,723 - __main__ - INFO - 
2025-05-30 02:56:59,723 - __main__ - INFO - 📊 调度器状态:
2025-05-30 02:56:59,723 - __main__ - INFO -    运行状态: 运行中
2025-05-30 02:56:59,723 - __main__ - INFO -    触发次数: 0
2025-05-30 02:56:59,724 - __main__ - INFO - 
2025-05-30 02:56:59,724 - __main__ - INFO - 🔄 调度器正在运行，等待触发条件...
2025-05-30 08:56:59,936 - core.training_scheduler - INFO - 📊 数据驱动检查
2025-05-30 08:56:59,938 - core.training_scheduler - INFO - 🔄 发现1857条新数据，触发训练
2025-05-30 08:56:59,938 - core.training_scheduler - INFO - 🚀 开始执行训练: 数据驱动训练(新增1857条)
2025-05-30 08:56:59,957 - core.training_scheduler - INFO - 训练进程已启动: PID 18044
2025-05-30 09:36:40,755 - __main__ - INFO - 🚀 QuantumForex MLTrainer 训练调度器
2025-05-30 09:36:40,756 - __main__ - INFO - ============================================================
2025-05-30 09:36:41,504 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 09:36:41,504 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 09:36:41,505 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 09:36:41,505 - __main__ - INFO - 📅 调度器功能:
2025-05-30 09:36:41,505 - __main__ - INFO -    ✅ 定时触发 - 根据配置的频率自动训练
2025-05-30 09:36:41,505 - __main__ - INFO -    ✅ 数据驱动 - 检测到足够新数据时自动训练
2025-05-30 09:36:41,505 - __main__ - INFO -    ✅ 性能驱动 - 模型性能下降时自动重训练
2025-05-30 09:36:41,505 - __main__ - INFO -    ✅ 手动触发 - 支持手动强制触发训练
2025-05-30 09:36:41,505 - __main__ - INFO - 
2025-05-30 09:36:41,506 - __main__ - INFO - 🔧 控制命令:
2025-05-30 09:36:41,506 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 09:36:41,506 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 09:36:41,506 - __main__ - INFO - 
2025-05-30 09:36:41,506 - __main__ - INFO - 📊 调度器状态:
2025-05-30 09:36:41,506 - __main__ - INFO -    运行状态: 运行中
2025-05-30 09:36:41,506 - __main__ - INFO -    触发次数: 0
2025-05-30 09:36:41,507 - __main__ - INFO - 
2025-05-30 09:36:41,507 - __main__ - INFO - 🔄 调度器正在运行，等待触发条件...
2025-05-30 09:45:55,714 - __main__ - INFO - 🚀 QuantumForex MLTrainer 训练调度器
2025-05-30 09:45:55,714 - __main__ - INFO - ============================================================
2025-05-30 09:45:56,248 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 09:45:56,248 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 09:45:56,249 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 09:45:56,249 - __main__ - INFO - 📅 调度器功能:
2025-05-30 09:45:56,250 - __main__ - INFO -    ✅ 定时触发 - 根据配置的频率自动训练
2025-05-30 09:45:56,250 - __main__ - INFO -    ✅ 数据驱动 - 检测到足够新数据时自动训练
2025-05-30 09:45:56,250 - __main__ - INFO -    ✅ 性能驱动 - 模型性能下降时自动重训练
2025-05-30 09:45:56,250 - __main__ - INFO -    ✅ 手动触发 - 支持手动强制触发训练
2025-05-30 09:45:56,250 - __main__ - INFO - 
2025-05-30 09:45:56,251 - __main__ - INFO - 🔧 控制命令:
2025-05-30 09:45:56,251 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 09:45:56,251 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 09:45:56,251 - __main__ - INFO - 
2025-05-30 09:45:56,251 - __main__ - INFO - 📊 调度器状态:
2025-05-30 09:45:56,252 - __main__ - INFO -    运行状态: 运行中
2025-05-30 09:45:56,252 - __main__ - INFO -    触发次数: 0
2025-05-30 09:45:56,252 - __main__ - INFO - 
2025-05-30 09:45:56,252 - __main__ - INFO - 🔄 调度器正在运行，等待触发条件...
2025-05-30 10:19:53,307 - __main__ - INFO - 🚀 QuantumForex MLTrainer 训练调度器
2025-05-30 10:19:53,307 - __main__ - INFO - ============================================================
2025-05-30 10:19:53,853 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 10:19:53,853 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 10:19:53,854 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 10:19:53,854 - __main__ - INFO - 📅 调度器功能:
2025-05-30 10:19:53,855 - __main__ - INFO -    ✅ 定时触发 - 根据配置的频率自动训练
2025-05-30 10:19:53,855 - __main__ - INFO -    ✅ 数据驱动 - 检测到足够新数据时自动训练
2025-05-30 10:19:53,855 - __main__ - INFO -    ✅ 性能驱动 - 模型性能下降时自动重训练
2025-05-30 10:19:53,855 - __main__ - INFO -    ✅ 手动触发 - 支持手动强制触发训练
2025-05-30 10:19:53,855 - __main__ - INFO - 
2025-05-30 10:19:53,855 - __main__ - INFO - 🔧 控制命令:
2025-05-30 10:19:53,855 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 10:19:53,856 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 10:19:53,856 - __main__ - INFO - 
2025-05-30 10:19:53,856 - __main__ - INFO - 📊 调度器状态:
2025-05-30 10:19:53,856 - __main__ - INFO -    运行状态: 运行中
2025-05-30 10:19:53,856 - __main__ - INFO -    触发次数: 0
2025-05-30 10:19:53,856 - __main__ - INFO - 
2025-05-30 10:19:53,857 - __main__ - INFO - 🔄 调度器正在运行，等待触发条件...
2025-05-30 10:36:19,677 - __main__ - INFO - 🚀 QuantumForex MLTrainer 训练调度器
2025-05-30 10:36:19,677 - __main__ - INFO - ============================================================
2025-05-30 10:36:20,236 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 10:36:20,237 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 10:36:20,238 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 10:36:20,238 - __main__ - INFO - 📅 调度器功能:
2025-05-30 10:36:20,238 - __main__ - INFO -    ✅ 定时触发 - 根据配置的频率自动训练
2025-05-30 10:36:20,239 - __main__ - INFO -    ✅ 数据驱动 - 检测到足够新数据时自动训练
2025-05-30 10:36:20,239 - __main__ - INFO -    ✅ 性能驱动 - 模型性能下降时自动重训练
2025-05-30 10:36:20,239 - __main__ - INFO -    ✅ 手动触发 - 支持手动强制触发训练
2025-05-30 10:36:20,239 - __main__ - INFO - 
2025-05-30 10:36:20,239 - __main__ - INFO - 🔧 控制命令:
2025-05-30 10:36:20,240 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 10:36:20,240 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 10:36:20,240 - __main__ - INFO - 
2025-05-30 10:36:20,240 - __main__ - INFO - 📊 调度器状态:
2025-05-30 10:36:20,240 - __main__ - INFO -    运行状态: 运行中
2025-05-30 10:36:20,241 - __main__ - INFO -    触发次数: 0
2025-05-30 10:36:20,241 - __main__ - INFO - 
2025-05-30 10:36:20,241 - __main__ - INFO - 🔄 调度器正在运行，等待触发条件...
2025-05-30 14:37:37,888 - __main__ - INFO - 🚀 QuantumForex MLTrainer 训练调度器
2025-05-30 14:37:37,889 - __main__ - INFO - ============================================================
2025-05-30 14:37:38,505 - core.training_scheduler - INFO - 🚀 启动训练调度器...
2025-05-30 14:37:38,506 - core.training_scheduler - INFO - 📅 定时任务设置完成: daily重训练
2025-05-30 14:37:38,507 - core.training_scheduler - INFO - ✅ 训练调度器启动成功
2025-05-30 14:37:38,507 - __main__ - INFO - 📅 调度器功能:
2025-05-30 14:37:38,507 - __main__ - INFO -    ✅ 定时触发 - 根据配置的频率自动训练
2025-05-30 14:37:38,507 - __main__ - INFO -    ✅ 数据驱动 - 检测到足够新数据时自动训练
2025-05-30 14:37:38,507 - __main__ - INFO -    ✅ 性能驱动 - 模型性能下降时自动重训练
2025-05-30 14:37:38,507 - __main__ - INFO -    ✅ 手动触发 - 支持手动强制触发训练
2025-05-30 14:37:38,510 - __main__ - INFO - 
2025-05-30 14:37:38,510 - __main__ - INFO - 🔧 控制命令:
2025-05-30 14:37:38,510 - __main__ - INFO -    Ctrl+C: 停止调度器
2025-05-30 14:37:38,511 - __main__ - INFO -    查看状态: 访问调度器状态接口
2025-05-30 14:37:38,511 - __main__ - INFO - 
2025-05-30 14:37:38,511 - __main__ - INFO - 📊 调度器状态:
2025-05-30 14:37:38,512 - __main__ - INFO -    运行状态: 运行中
2025-05-30 14:37:38,512 - __main__ - INFO -    触发次数: 0
2025-05-30 14:37:38,512 - __main__ - INFO - 
2025-05-30 14:37:38,512 - __main__ - INFO - 🔄 调度器正在运行，等待触发条件...
